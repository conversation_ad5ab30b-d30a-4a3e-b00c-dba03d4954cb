-- 数据库时间管理函数
-- 解决服务器时间与数据库时间不同步的问题

-- 1. 获取数据库当前UTC时间
CREATE OR REPLACE FUNCTION current_timestamp_utc()
RETURNS TIMESTAMP WITH TIME ZONE AS $$
BEGIN
    RETURN NOW() AT TIME ZONE 'UTC';
END;
$$ LANGUAGE plpgsql;

-- 2. 创建带偏移的时间戳
CREATE OR REPLACE FUNCTION create_timestamp_with_offset(offset_minutes INTEGER)
RETURNS TIMESTAMP WITH TIME ZONE AS $$
BEGIN
    RETURN (NOW() AT TIME ZONE 'UTC') + (offset_minutes || ' minutes')::INTERVAL;
END;
$$ LANGUAGE plpgsql;

-- 3. 检查时间戳是否过期
CREATE OR REPLACE FUNCTION is_timestamp_expired(expires_at TIMESTAMP WITH TIME ZONE)
RETURNS BOOLEAN AS $$
BEGIN
    RETURN expires_at < (NOW() AT TIME ZONE 'UTC');
END;
$$ LANGUAGE plpgsql;

-- 4. 计算两个时间戳的差异（秒）
CREATE OR REPLACE FUNCTION get_timestamp_difference(
    timestamp1 TIMESTAMP WITH TIME ZONE,
    timestamp2 TIMESTAMP WITH TIME ZONE DEFAULT NULL
)
RETURNS INTEGER AS $$
BEGIN
    IF timestamp2 IS NULL THEN
        timestamp2 := NOW() AT TIME ZONE 'UTC';
    END IF;
    
    RETURN EXTRACT(EPOCH FROM (timestamp2 - timestamp1))::INTEGER;
END;
$$ LANGUAGE plpgsql;

-- 5. 检查会话是否有效（带宽限期）
CREATE OR REPLACE FUNCTION is_session_valid(
    expires_at TIMESTAMP WITH TIME ZONE,
    grace_period_minutes INTEGER DEFAULT 5
)
RETURNS BOOLEAN AS $$
BEGIN
    RETURN expires_at > ((NOW() AT TIME ZONE 'UTC') - (grace_period_minutes || ' minutes')::INTERVAL);
END;
$$ LANGUAGE plpgsql;

-- 6. 清理过期会话的函数（改进版）
CREATE OR REPLACE FUNCTION cleanup_expired_sessions()
RETURNS INTEGER AS $$
DECLARE
    deleted_count INTEGER := 0;
BEGIN
    -- 删除过期的用户会话
    DELETE FROM user_sessions 
    WHERE expires_at < (NOW() AT TIME ZONE 'UTC');
    
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    
    -- 记录清理日志
    INSERT INTO login_logs (email, auth_type, status, created_at)
    VALUES ('system', 'session_cleanup', 'success', NOW() AT TIME ZONE 'UTC')
    ON CONFLICT DO NOTHING;
    
    RETURN deleted_count;
END;
$$ LANGUAGE plpgsql;

-- 7. 获取用户的活跃会话数
CREATE OR REPLACE FUNCTION get_user_active_sessions(user_uuid UUID)
RETURNS INTEGER AS $$
BEGIN
    RETURN (
        SELECT COUNT(*)
        FROM user_sessions 
        WHERE user_id = user_uuid 
        AND expires_at > (NOW() AT TIME ZONE 'UTC')
    );
END;
$$ LANGUAGE plpgsql;

-- 8. 延长会话有效期
CREATE OR REPLACE FUNCTION extend_session_expiry(
    session_token_param VARCHAR(128),
    extend_hours INTEGER DEFAULT 24
)
RETURNS BOOLEAN AS $$
DECLARE
    session_exists BOOLEAN := FALSE;
BEGIN
    -- 检查会话是否存在且未过期
    SELECT EXISTS(
        SELECT 1 FROM user_sessions 
        WHERE session_token = session_token_param
        AND expires_at > (NOW() AT TIME ZONE 'UTC')
    ) INTO session_exists;
    
    IF session_exists THEN
        -- 延长会话
        UPDATE user_sessions 
        SET 
            expires_at = (NOW() AT TIME ZONE 'UTC') + (extend_hours || ' hours')::INTERVAL,
            last_activity = NOW() AT TIME ZONE 'UTC'
        WHERE session_token = session_token_param;
        
        RETURN TRUE;
    END IF;
    
    RETURN FALSE;
END;
$$ LANGUAGE plpgsql;

-- 9. 创建会话的安全函数
CREATE OR REPLACE FUNCTION create_user_session(
    user_uuid UUID,
    token VARCHAR(128),
    ip_address_param INET DEFAULT NULL,
    user_agent_param TEXT DEFAULT NULL,
    expire_hours INTEGER DEFAULT 24
)
RETURNS UUID AS $$
DECLARE
    session_id UUID;
    current_time TIMESTAMP WITH TIME ZONE;
    expiry_time TIMESTAMP WITH TIME ZONE;
BEGIN
    current_time := NOW() AT TIME ZONE 'UTC';
    expiry_time := current_time + (expire_hours || ' hours')::INTERVAL;
    
    -- 创建新会话
    INSERT INTO user_sessions (
        user_id, 
        session_token, 
        expires_at, 
        ip_address, 
        user_agent, 
        created_at,
        last_activity
    ) VALUES (
        user_uuid,
        token,
        expiry_time,
        ip_address_param,
        user_agent_param,
        current_time,
        current_time
    ) RETURNING id INTO session_id;
    
    RETURN session_id;
END;
$$ LANGUAGE plpgsql;

-- 10. 验证会话令牌的安全函数
CREATE OR REPLACE FUNCTION validate_session_token(token VARCHAR(128))
RETURNS TABLE(
    session_id UUID,
    user_id UUID,
    expires_at TIMESTAMP WITH TIME ZONE,
    is_valid BOOLEAN,
    time_until_expiry INTEGER
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        s.id,
        s.user_id,
        s.expires_at,
        (s.expires_at > (NOW() AT TIME ZONE 'UTC')) AS is_valid,
        EXTRACT(EPOCH FROM (s.expires_at - (NOW() AT TIME ZONE 'UTC')))::INTEGER AS time_until_expiry
    FROM user_sessions s
    WHERE s.session_token = token;
END;
$$ LANGUAGE plpgsql;

-- 创建索引优化性能
CREATE INDEX IF NOT EXISTS idx_user_sessions_expires_at_valid 
ON user_sessions(expires_at) 
WHERE expires_at > NOW();

CREATE INDEX IF NOT EXISTS idx_user_sessions_token_valid 
ON user_sessions(session_token) 
WHERE expires_at > NOW();

-- 创建定时清理任务（如果支持pg_cron扩展）
-- SELECT cron.schedule('cleanup-expired-sessions', '0 */2 * * *', 'SELECT cleanup_expired_sessions();');

-- 添加注释
COMMENT ON FUNCTION current_timestamp_utc() IS '获取数据库当前UTC时间';
COMMENT ON FUNCTION create_timestamp_with_offset(INTEGER) IS '创建带偏移的时间戳';
COMMENT ON FUNCTION is_timestamp_expired(TIMESTAMP WITH TIME ZONE) IS '检查时间戳是否过期';
COMMENT ON FUNCTION get_timestamp_difference(TIMESTAMP WITH TIME ZONE, TIMESTAMP WITH TIME ZONE) IS '计算两个时间戳的差异（秒）';
COMMENT ON FUNCTION is_session_valid(TIMESTAMP WITH TIME ZONE, INTEGER) IS '检查会话是否有效（带宽限期）';
COMMENT ON FUNCTION cleanup_expired_sessions() IS '清理过期会话';
COMMENT ON FUNCTION get_user_active_sessions(UUID) IS '获取用户的活跃会话数';
COMMENT ON FUNCTION extend_session_expiry(VARCHAR, INTEGER) IS '延长会话有效期';
COMMENT ON FUNCTION create_user_session(UUID, VARCHAR, INET, TEXT, INTEGER) IS '创建用户会话';
COMMENT ON FUNCTION validate_session_token(VARCHAR) IS '验证会话令牌';

-- 手动执行一次清理
SELECT cleanup_expired_sessions() as cleaned_sessions;