import { NextRequest, NextResponse } from "next/server"
import { verifyEmailCode, createUserWithPassword } from "@/lib/enhanced-email-auth"
import { supabaseAdmin } from "@/lib/supabase"
import crypto from 'crypto'

/**
 * 邮箱注册 API
 * POST /api/auth/email/register
 */
export async function POST(request: NextRequest) {
  try {
    const { email, code, password } = await request.json()
    
    if (!email || !code || !password) {
      return NextResponse.json({
        success: false,
        error: "请填写所有必要信息"
      }, { status: 400 })
    }
    
    console.log('🔍 邮箱注册请求:', { email })
    
    // 🔧 修复：先检查邮箱是否已存在，避免浪费验证码
    const { data: existingUser, error: checkError } = await supabaseAdmin
      .from('users')
      .select('id, created_at')
      .eq('email', email)
      .single()
    
    if (existingUser) {
      console.log('⚠️ 邮箱已存在:', { email, userId: existingUser.id, createdAt: existingUser.created_at })
      return NextResponse.json({
        success: false,
        error: "该邮箱已被注册，请直接登录或使用其他邮箱"
      }, { status: 400 })
    }
    
    console.log('✅ 邮箱可用，开始验证验证码')
    
    // 验证邮箱验证码
    const verifyResult = await verifyEmailCode(email, code, 'register')
    
    if (!verifyResult.valid) {
      console.log('❌ 验证码验证失败:', verifyResult.error)
      return NextResponse.json({
        success: false,
        error: verifyResult.error
      }, { status: 400 })
    }
    
    console.log('✅ 验证码验证成功，开始创建用户')
    
    // 创建用户
    const createResult = await createUserWithPassword(email, password)
    
    if (!createResult.success) {
      return NextResponse.json({
        success: false,
        error: createResult.error
      }, { status: 400 })
    }
    
    // 生成用户会话令牌
    const sessionToken = crypto.randomBytes(32).toString('hex')
    
    console.log('🔐 创建注册会话令牌:', { sessionToken: sessionToken.substring(0, 8) + '...' })
    
    console.log('✅ 邮箱注册成功:', { 
      email, 
      userId: createResult.user.id,
      sessionToken: sessionToken.substring(0, 8) + '...'
    })
    
    // 返回成功响应和用户信息
    const response = NextResponse.json({
      success: true,
      message: "注册成功",
      user: {
        id: createResult.user.id,
        email: createResult.user.email,
        username: createResult.user.username,
        nickname: createResult.user.nickname,
        avatar_url: createResult.user.avatar_url,
        auth_type: createResult.user.auth_type,
        email_verified: createResult.user.email_verified,
        has_password: true,
        created_at: createResult.user.created_at
      }
    })
    
    // 设置会话Cookie
    response.cookies.set('session_token', sessionToken, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'lax',
      maxAge: 24 * 60 * 60, // 24小时
      path: '/'
    })
    
    return response
    
  } catch (error) {
    console.error('❌ 邮箱注册异常:', error)
    
    return NextResponse.json({
      success: false,
      error: "注册过程出现错误"
    }, { status: 500 })
  }
}