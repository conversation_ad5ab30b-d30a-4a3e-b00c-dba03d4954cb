import { NextRequest, NextResponse } from "next/server"
import { verifyEmailCode, createUserWithPassword } from "@/lib/enhanced-email-auth"
import { supabaseAdmin } from "@/lib/supabase"
import crypto from 'crypto'

/**
 * 邮箱注册 API
 * POST /api/auth/email/register
 */
export async function POST(request: NextRequest) {
  try {
    const { email, code, password } = await request.json()
    
    if (!email || !code || !password) {
      return NextResponse.json({
        success: false,
        error: "请填写所有必要信息"
      }, { status: 400 })
    }
    
    console.log('🔍 邮箱注册请求:', { email })
    
    // 验证邮箱验证码
    const verifyResult = await verifyEmailCode(email, code, 'register')
    
    if (!verifyResult.valid) {
      return NextResponse.json({
        success: false,
        error: verifyResult.error
      }, { status: 400 })
    }
    
    // 检查邮箱是否已存在
    const { data: existingUser } = await supabaseAdmin
      .from('users')
      .select('id')
      .eq('email', email)
      .single()
    
    if (existingUser) {
      return NextResponse.json({
        success: false,
        error: "邮箱已被注册"
      }, { status: 400 })
    }
    
    // 创建用户
    const createResult = await createUserWithPassword(email, password)
    
    if (!createResult.success) {
      return NextResponse.json({
        success: false,
        error: createResult.error
      }, { status: 400 })
    }
    
    // 生成用户会话令牌
    const sessionToken = crypto.randomBytes(32).toString('hex')
    const expiresAt = new Date(Date.now() + 24 * 60 * 60 * 1000) // 24小时
    
    // 保存会话
    await supabaseAdmin
      .from('user_sessions')
      .insert({
        user_id: createResult.user.id,
        session_token: sessionToken,
        expires_at: expiresAt.toISOString(),
        ip_address: request.ip || 'unknown',
        user_agent: request.headers.get('user-agent') || 'unknown',
        created_at: new Date().toISOString()
      })
    
    // 记录注册日志
    await supabaseAdmin
      .from('login_logs')
      .insert({
        user_id: createResult.user.id,
        email,
        auth_type: 'register',
        status: 'success',
        ip_address: request.ip || 'unknown',
        user_agent: request.headers.get('user-agent') || 'unknown',
        created_at: new Date().toISOString()
      })
    
    console.log('✅ 邮箱注册成功:', { 
      email, 
      userId: createResult.user.id,
      sessionToken: sessionToken.substring(0, 8) + '...'
    })
    
    // 返回成功响应和用户信息
    const response = NextResponse.json({
      success: true,
      message: "注册成功",
      user: {
        id: createResult.user.id,
        email: createResult.user.email,
        username: createResult.user.username,
        nickname: createResult.user.nickname,
        avatar_url: createResult.user.avatar_url,
        auth_type: createResult.user.auth_type,
        email_verified: createResult.user.email_verified,
        has_password: true,
        created_at: createResult.user.created_at
      }
    })
    
    // 设置会话Cookie
    response.cookies.set('session_token', sessionToken, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'lax',
      maxAge: 24 * 60 * 60, // 24小时
      path: '/'
    })
    
    return response
    
  } catch (error) {
    console.error('❌ 邮箱注册异常:', error)
    
    return NextResponse.json({
      success: false,
      error: "注册过程出现错误"
    }, { status: 500 })
  }
}