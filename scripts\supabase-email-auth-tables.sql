-- 邮箱验证码认证系统 - 增量更新脚本
-- 在 Supabase SQL Editor 中执行此脚本
-- 此脚本会在现有表基础上添加缺失的字段和表

-- 1. 为现有users表添加缺失的密码相关字段
ALTER TABLE users 
ADD COLUMN IF NOT EXISTS password_hash TEXT,
ADD COLUMN IF NOT EXISTS has_password BOOLEAN DEFAULT FALSE,
ADD COLUMN IF NOT EXISTS password_updated_at TIMESTAMP WITH TIME ZONE;

-- 2. 创建邮箱验证码表 (关键表)
CREATE TABLE IF NOT EXISTS email_codes (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  email VARCHAR(255) NOT NULL,
  code VARCHAR(10) NOT NULL,
  type VARCHAR(20) DEFAULT 'login',
  expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
  used BOOLEAN DEFAULT FALSE,
  used_at TIMESTAMP WITH TIME ZONE,
  attempts INTEGER DEFAULT 0,
  ip_address INET,
  user_agent TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 3. 为现有users表创建缺失的索引
CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);
CREATE INDEX IF NOT EXISTS idx_users_auth_type ON users(auth_type);
CREATE INDEX IF NOT EXISTS idx_users_has_password ON users(has_password);

-- 4. 为email_codes表创建索引
CREATE INDEX IF NOT EXISTS idx_email_codes_email ON email_codes(email);
CREATE INDEX IF NOT EXISTS idx_email_codes_code ON email_codes(code);
CREATE INDEX IF NOT EXISTS idx_email_codes_type ON email_codes(type);
CREATE INDEX IF NOT EXISTS idx_email_codes_expires_at ON email_codes(expires_at);
CREATE INDEX IF NOT EXISTS idx_email_codes_used ON email_codes(used);

-- 5. 为现有user_sessions表添加缺失的字段 (如果需要)
ALTER TABLE user_sessions 
ADD COLUMN IF NOT EXISTS ip_address INET,
ADD COLUMN IF NOT EXISTS user_agent TEXT,
ADD COLUMN IF NOT EXISTS last_activity TIMESTAMP WITH TIME ZONE DEFAULT NOW();

-- 6. 为现有login_logs表添加缺失的索引
CREATE INDEX IF NOT EXISTS idx_login_logs_user_id ON login_logs(user_id);
CREATE INDEX IF NOT EXISTS idx_login_logs_email ON login_logs(email);
CREATE INDEX IF NOT EXISTS idx_login_logs_auth_type ON login_logs(auth_type);
CREATE INDEX IF NOT EXISTS idx_login_logs_created_at ON login_logs(created_at);

-- 7. 为现有user_sessions表创建索引
CREATE INDEX IF NOT EXISTS idx_user_sessions_user_id ON user_sessions(user_id);
CREATE INDEX IF NOT EXISTS idx_user_sessions_token ON user_sessions(session_token);
CREATE INDEX IF NOT EXISTS idx_user_sessions_expires_at ON user_sessions(expires_at);

-- 8. 插入邮箱认证相关的系统配置
INSERT INTO system_config (config_key, config_value, description) VALUES
('email_auth_enabled', 'true', '是否启用邮箱验证码认证'),
('password_auth_enabled', 'true', '是否启用密码认证'),
('code_expires_minutes', '10', '验证码有效期(分钟)'),
('max_code_attempts', '5', '验证码最大尝试次数'),
('max_codes_per_hour', '10', '每小时最大验证码请求次数'),
('password_min_length', '6', '密码最小长度'),
('password_history_limit', '5', '密码历史记录保留数量')
ON CONFLICT (config_key) DO UPDATE SET
    config_value = EXCLUDED.config_value,
    description = EXCLUDED.description,
    updated_at = NOW();

-- 9. 验证表结构
SELECT 'users表字段数量:' as info, count(*) as count FROM information_schema.columns WHERE table_name = 'users';
SELECT 'email_codes表字段数量:' as info, count(*) as count FROM information_schema.columns WHERE table_name = 'email_codes';

-- 完成提示
SELECT '✅ 邮箱验证码认证系统数据表更新完成！' as status;