import { NextRequest, NextResponse } from "next/server"
import { verifyEmailCode, loginWithPassword } from "@/lib/enhanced-email-auth"
import { supabaseAdmin } from "@/lib/supabase"
import crypto from 'crypto'

/**
 * 邮箱验证码登录 / 密码登录 API
 * POST /api/auth/email/login
 */
export async function POST(request: NextRequest) {
  try {
    const { email, code, password, loginType = 'code' } = await request.json()
    
    if (!email) {
      return NextResponse.json({
        success: false,
        error: "请输入邮箱地址"
      }, { status: 400 })
    }
    
    console.log('🔍 邮箱登录请求:', { email, loginType })
    
    let result
    
    if (loginType === 'password') {
      // 密码登录
      if (!password) {
        return NextResponse.json({
          success: false,
          error: "请输入密码"
        }, { status: 400 })
      }
      
      result = await loginWithPassword(email, password)
      
      if (!result.success) {
        return NextResponse.json({
          success: false,
          error: result.error
        }, { status: 400 })
      }
    } else {
      // 验证码登录
      if (!code) {
        return NextResponse.json({
          success: false,
          error: "请输入验证码"
        }, { status: 400 })
      }
      
      const verifyResult = await verifyEmailCode(email, code, 'login')
      
      if (!verifyResult.valid) {
        return NextResponse.json({
          success: false,
          error: verifyResult.error
        }, { status: 400 })
      }
      
      result = {
        success: true,
        user: verifyResult.user,
        isNewUser: verifyResult.isNewUser
      }
    }
    
    // 生成用户会话令牌
    const sessionToken = crypto.randomBytes(32).toString('hex')
    
    console.log('🔐 创建会话令牌:', { sessionToken: sessionToken.substring(0, 8) + '...' })
    
    // 保存会话 - 使用数据库时间而不是服务器时间
    const { data: sessionData, error: sessionError } = await supabaseAdmin
      .from('user_sessions')
      .insert({
        user_id: result.user.id,
        session_token: sessionToken,
        expires_at: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(), // 临时使用服务器时间
        ip_address: request.ip || 'unknown',
        user_agent: request.headers.get('user-agent') || 'unknown'
        // created_at 将使用数据库默认值 NOW()
      })
      .select('id, created_at, expires_at')
      .single()
    
    if (sessionError) {
      console.error('❌ 会话创建失败:', sessionError)
      return NextResponse.json({
        success: false,
        error: "登录失败，请稍后重试"
      }, { status: 500 })
    }
    
    // 更新会话过期时间为数据库时间 + 24小时
    const { error: updateError } = await supabaseAdmin
      .from('user_sessions')
      .update({
        expires_at: new Date(new Date(sessionData.created_at).getTime() + 24 * 60 * 60 * 1000).toISOString()
      })
      .eq('id', sessionData.id)
    
    if (updateError) {
      console.warn('⚠️ 会话过期时间更新失败:', updateError.message)
    }
    
    // 记录成功登录日志 - 使用数据库默认时间
    await supabaseAdmin
      .from('login_logs')
      .insert({
        user_id: result.user.id,
        email,
        auth_type: loginType === 'password' ? 'password_login' : 'email_code',
        status: 'success',
        ip_address: request.ip || 'unknown',
        user_agent: request.headers.get('user-agent') || 'unknown'
        // created_at 将使用数据库默认值 NOW()
      })
    
    console.log('✅ 邮箱登录成功:', { 
      email, 
      loginType,
      userId: result.user.id,
      isNewUser: result.isNewUser,
      sessionToken: sessionToken.substring(0, 8) + '...'
    })
    
    // 返回成功响应和用户信息
    const response = NextResponse.json({
      success: true,
      message: result.isNewUser ? "注册并登录成功" : "登录成功",
      isNewUser: result.isNewUser,
      user: {
        id: result.user.id,
        email: result.user.email,
        username: result.user.username,
        nickname: result.user.nickname,
        avatar_url: result.user.avatar_url,
        auth_type: result.user.auth_type,
        email_verified: result.user.email_verified,
        has_password: result.user.has_password,
        created_at: result.user.created_at
      }
    })
    
    // 设置会话Cookie
    response.cookies.set('session_token', sessionToken, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'lax',
      maxAge: 24 * 60 * 60, // 24小时
      path: '/'
    })
    
    return response
    
  } catch (error) {
    console.error('❌ 邮箱登录异常:', error)
    
    return NextResponse.json({
      success: false,
      error: "登录过程出现错误"
    }, { status: 500 })
  }
}