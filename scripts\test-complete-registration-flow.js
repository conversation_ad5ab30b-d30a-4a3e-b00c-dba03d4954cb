require('dotenv').config({ path: '.env.local' });
const { createClient } = require('@supabase/supabase-js');

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

const supabaseAdmin = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

async function testCompleteRegistrationFlow() {
  const testEmail = `test+${Date.now()}@example.com`;
  const verifyCode = '123456';
  const password = 'testpass123';
  
  console.log('🧪 开始完整注册流程测试...');
  console.log('测试邮箱:', testEmail);
  
  try {
    // 步骤1: 生成验证码
    console.log('\n1. 模拟发送验证码...');
    const { data: codeInsert, error: codeError } = await supabaseAdmin
      .from('email_codes')
      .insert({
        email: testEmail,
        code: verifyCode,
        type: 'register',
        expires_at: new Date(Date.now() + 10 * 60 * 1000).toISOString(),
        used: false,
        attempts: 0
      })
      .select();

    if (codeError) {
      console.error('❌ 验证码创建失败:', codeError.message);
      return;
    }
    
    console.log('✅ 验证码创建成功');

    // 步骤2: 测试注册API
    console.log('\n2. 测试注册API...');
    const registerResponse = await fetch('http://localhost:3000/api/auth/email/register', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email: testEmail,
        code: verifyCode,
        password: password
      })
    });

    const registerData = await registerResponse.json();
    console.log('注册响应状态:', registerResponse.status);
    console.log('注册响应数据:', registerData);

    if (!registerData.success) {
      console.error('❌ 注册失败:', registerData.error);
      return;
    }

    console.log('✅ 注册成功！');
    console.log('用户信息:', {
      id: registerData.user.id,
      email: registerData.user.email,
      username: registerData.user.username,
      nickname: registerData.user.nickname,
      has_password: registerData.user.has_password
    });

    // 步骤3: 验证用户在数据库中的完整性
    console.log('\n3. 验证数据库中的用户数据...');
    const { data: dbUser, error: userError } = await supabaseAdmin
      .from('users')
      .select('*')
      .eq('email', testEmail)
      .single();

    if (userError) {
      console.error('❌ 获取用户数据失败:', userError.message);
      return;
    }

    console.log('✅ 用户数据验证成功:');
    console.log('- ID:', dbUser.id);
    console.log('- Email:', dbUser.email);
    console.log('- Username:', dbUser.username);
    console.log('- Nickname:', dbUser.nickname);
    console.log('- Auth Type:', dbUser.auth_type);
    console.log('- Has Password:', dbUser.has_password);
    console.log('- Password Hash存在:', !!dbUser.password_hash);
    console.log('- Email Verified:', dbUser.email_verified);
    console.log('- Status:', dbUser.status);
    console.log('- Login Count:', dbUser.login_count);

    // 步骤4: 测试重复注册（应该失败）
    console.log('\n4. 测试重复注册保护...');
    
    // 创建新验证码
    await supabaseAdmin
      .from('email_codes')
      .insert({
        email: testEmail,
        code: '654321',
        type: 'register',
        expires_at: new Date(Date.now() + 10 * 60 * 1000).toISOString(),
        used: false,
        attempts: 0
      });

    const duplicateResponse = await fetch('http://localhost:3000/api/auth/email/register', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email: testEmail,
        code: '654321',
        password: 'newpass123'
      })
    });

    const duplicateData = await duplicateResponse.json();
    
    if (!duplicateData.success && duplicateData.error.includes('已被注册')) {
      console.log('✅ 重复注册保护正常工作');
    } else {
      console.error('❌ 重复注册保护失败:', duplicateData);
    }

    console.log('\n🎉 所有测试通过，注册流程修复成功！');

  } catch (error) {
    console.error('❌ 测试过程中发生错误:', error.message);
  }
}

testCompleteRegistrationFlow();