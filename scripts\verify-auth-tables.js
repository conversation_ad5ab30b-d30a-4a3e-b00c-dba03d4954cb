// 验证Supabase认证表结构创建结果
const { createClient } = require('@supabase/supabase-js');
require('dotenv').config({ path: '.env.local' });

// 创建管理员客户端
const supabaseAdmin = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY,
  {
    auth: {
      autoRefreshToken: false,
      persistSession: false
    }
  }
);

async function verifyTablesCreation() {
  console.log('🔍 验证Supabase认证表结构创建结果');
  console.log('=' .repeat(50));
  
  try {
    // 需要验证的表列表
    const requiredTables = [
      { name: 'users', description: '用户基础信息表' },
      { name: 'magic_links', description: '魔法链接表' },
      { name: 'phone_codes', description: '验证码表' },
      { name: 'user_sessions', description: '用户会话表' },
      { name: 'login_logs', description: '登录日志表' },
      { name: 'social_accounts', description: '社交账号表' }
    ];
    
    let successCount = 0;
    let failedTables = [];
    
    console.log('📋 检查表结构创建状态...\n');
    
    for (const table of requiredTables) {
      try {
        // 尝试查询表结构
        const { data, error } = await supabaseAdmin
          .from(table.name)
          .select('*')
          .limit(1);
        
        if (error) {
          console.log(`❌ ${table.name} - ${table.description}`);
          console.log(`   错误: ${error.message}\n`);
          failedTables.push(table.name);
        } else {
          console.log(`✅ ${table.name} - ${table.description}`);
          successCount++;
        }
      } catch (err) {
        console.log(`❌ ${table.name} - ${table.description}`);
        console.log(`   异常: ${err.message}\n`);
        failedTables.push(table.name);
      }
    }
    
    console.log('\\n' + '=' .repeat(50));
    console.log(`📊 验证结果汇总: ${successCount}/${requiredTables.length} 个表创建成功`);
    
    if (successCount === requiredTables.length) {
      console.log('🎉 所有认证表创建成功！');
      
      // 测试基本功能
      console.log('\\n🧪 执行功能测试...');
      await testBasicFunctions();
      
    } else {
      console.log(`⚠️ ${failedTables.length} 个表创建失败:`, failedTables.join(', '));
      console.log('\\n💡 解决方案:');
      console.log('1. 检查SQL脚本是否完整执行');
      console.log('2. 确认Supabase项目权限');
      console.log('3. 查看SQL执行错误信息');
    }
    
    return successCount === requiredTables.length;
    
  } catch (error) {
    console.error('❌ 验证过程异常:', error);
    return false;
  }
}

async function testBasicFunctions() {
  try {
    // 测试1: 插入测试用户
    console.log('\\n1. 测试用户表插入...');
    const testEmail = `test_${Date.now()}@example.com`;
    
    const { data: newUser, error: insertError } = await supabaseAdmin
      .from('users')
      .insert({
        email: testEmail,
        nickname: '测试用户',
        auth_type: 'email'
      })
      .select()
      .single();
    
    if (insertError) {
      console.log('❌ 用户插入失败:', insertError.message);
      return;
    }
    
    console.log('✅ 用户插入成功:', newUser.email);
    
    // 测试2: 插入魔法链接记录
    console.log('\\n2. 测试魔法链接表插入...');
    const testToken = 'test_token_' + Date.now();
    const expiresAt = new Date(Date.now() + 10 * 60 * 1000); // 10分钟后过期
    
    const { data: magicLink, error: linkError } = await supabaseAdmin
      .from('magic_links')
      .insert({
        email: testEmail,
        token: testToken,
        signature: 'test_signature',
        expires_at: expiresAt.toISOString()
      })
      .select()
      .single();
    
    if (linkError) {
      console.log('❌ 魔法链接插入失败:', linkError.message);
    } else {
      console.log('✅ 魔法链接插入成功');
    }
    
    // 测试3: 查询功能
    console.log('\\n3. 测试查询功能...');
    const { data: users, error: queryError } = await supabaseAdmin
      .from('users')
      .select('*')
      .eq('email', testEmail);
    
    if (queryError) {
      console.log('❌ 查询失败:', queryError.message);
    } else {
      console.log('✅ 查询成功，找到用户:', users.length);
    }
    
    // 清理测试数据
    console.log('\\n4. 清理测试数据...');
    await supabaseAdmin.from('magic_links').delete().eq('email', testEmail);
    await supabaseAdmin.from('users').delete().eq('email', testEmail);
    console.log('✅ 测试数据清理完成');
    
    console.log('\\n🎯 基本功能测试通过！系统已就绪');
    
  } catch (error) {
    console.error('❌ 功能测试异常:', error);
  }
}

// 运行验证
verifyTablesCreation()
  .then(success => {
    if (success) {
      console.log('\\n🚀 认证系统数据库配置完成！');
      console.log('💡 现在可以启动开发服务器测试登录功能');
    } else {
      console.log('\\n⚠️ 请先完成表结构创建，然后重新运行验证');
    }
    process.exit(success ? 0 : 1);
  })
  .catch(error => {
    console.error('验证过程出错:', error);
    process.exit(1);
  });