// 检查email_codes表中的频率限制数据
const { createClient } = require('@supabase/supabase-js');
require('dotenv').config({ path: '.env.local' });

const supabaseAdmin = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY,
  {
    auth: {
      autoRefreshToken: false,
      persistSession: false
    }
  }
);

async function checkEmailCodesRateLimit() {
  console.log('🔍 检查email_codes表频率限制数据');
  console.log('=' .repeat(50));
  
  try {
    const testEmail = '<EMAIL>';
    
    // 1. 检查所有验证码记录
    console.log('📊 检查邮箱验证码记录...');
    const { data: allCodes, error: allError } = await supabaseAdmin
      .from('email_codes')
      .select('*')
      .eq('email', testEmail)
      .order('created_at', { ascending: false });
    
    if (allError) {
      console.error('❌ 查询失败:', allError.message);
      return;
    }
    
    console.log(`📈 总记录数: ${allCodes.length}`);
    
    if (allCodes.length > 0) {
      console.log('\\n📋 验证码记录详情:');
      allCodes.forEach((code, index) => {
        const isExpired = new Date(code.expires_at) < new Date();
        const isUsed = code.used;
        const age = Math.round((Date.now() - new Date(code.created_at)) / 1000 / 60); // 分钟
        
        console.log(`${index + 1}. 类型: ${code.type}, 创建: ${age}分钟前, 过期: ${isExpired ? '是' : '否'}, 已用: ${isUsed ? '是' : '否'}, 尝试次数: ${code.attempts}`);
      });
    }
    
    // 2. 检查1小时内的记录（当前限制逻辑）
    console.log('\\n⏰ 检查1小时内验证码记录...');
    const oneHourAgo = new Date(Date.now() - 3600000).toISOString(); // 1小时前
    
    const { data: recentCodes, error: recentError } = await supabaseAdmin
      .from('email_codes')
      .select('*')
      .eq('email', testEmail)
      .gte('created_at', oneHourAgo);
    
    if (recentError) {
      console.error('❌ 查询失败:', recentError.message);
      return;
    }
    
    console.log(`📊 1小时内记录数: ${recentCodes.length}`);
    console.log(`🚦 当前限制: 10次/小时, 状态: ${recentCodes.length >= 10 ? '已触发' : '正常'}`);
    
    // 3. 分析问题
    console.log('\\n🔍 问题分析:');
    const activeCodes = recentCodes.filter(code => 
      !code.used && new Date(code.expires_at) > new Date()
    );
    
    console.log(`📌 有效未使用验证码: ${activeCodes.length}`);
    console.log(`📌 已使用验证码: ${recentCodes.filter(c => c.used).length}`);
    console.log(`📌 已过期验证码: ${recentCodes.filter(c => new Date(c.expires_at) < new Date()).length}`);
    
    // 4. 检查是否有异常大量请求
    if (recentCodes.length >= 5) {
      console.log('\\n⚠️ 警告：1小时内验证码请求较多');
      console.log('可能原因：');
      console.log('1. 频繁刷新页面或重复点击');
      console.log('2. 测试过程中的多次尝试');
      console.log('3. 网络问题导致的重复请求');
    }
    
    // 5. 提供解决方案
    console.log('\\n💡 解决方案:');
    if (recentCodes.length > 0) {
      console.log('1. 清理测试数据：DELETE FROM email_codes WHERE email = \'<EMAIL>\';');
      console.log('2. 等待过期自动清理（验证码通常10分钟过期）');
      console.log('3. 调整频率限制参数（如果是合理使用）');
    }
    
    // 6. 尝试清理过期数据
    console.log('\\n🧹 清理过期验证码...');
    const { data: cleanupResult, error: cleanupError } = await supabaseAdmin
      .from('email_codes')
      .delete()
      .lt('expires_at', new Date().toISOString());
    
    if (cleanupError) {
      console.log('❌ 清理失败:', cleanupError.message);
    } else {
      console.log('✅ 过期验证码清理完成');
    }
    
  } catch (error) {
    console.error('❌ 检查过程异常:', error);
  }
}

checkEmailCodesRateLimit().then(() => process.exit(0));