/**
 * 创建邮箱验证码认证系统所需的数据库表
 * 执行方式：node scripts/setup-email-auth-tables.js
 */

require('dotenv').config({ path: '.env.local' })
const { createClient } = require('@supabase/supabase-js')

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ 缺少 Supabase 环境变量')
  console.error('需要设置: NEXT_PUBLIC_SUPABASE_URL 和 SUPABASE_SERVICE_ROLE_KEY')
  process.exit(1)
}

const supabase = createClient(supabaseUrl, supabaseServiceKey)

async function setupEmailAuthTables() {
  try {
    console.log('🚀 开始创建邮箱验证码认证系统数据表...')
    console.log('🔄 使用 .sql() 方法执行...')
    
    // 分别执行各个表的创建
    const tables = [
      // 用户表
      `CREATE TABLE IF NOT EXISTS users (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        email VARCHAR(255) UNIQUE,
        username VARCHAR(100),
        nickname VARCHAR(100),
        avatar_url TEXT,
        auth_type VARCHAR(50) DEFAULT 'email',
        email_verified BOOLEAN DEFAULT FALSE,
        phone VARCHAR(20),
        phone_verified BOOLEAN DEFAULT FALSE,
        password_hash TEXT,
        has_password BOOLEAN DEFAULT FALSE,
        password_updated_at TIMESTAMP WITH TIME ZONE,
        status VARCHAR(20) DEFAULT 'active',
        login_count INTEGER DEFAULT 0,
        last_login_at TIMESTAMP WITH TIME ZONE,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
      );`,
      
      // 邮箱验证码表
      `CREATE TABLE IF NOT EXISTS email_codes (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        email VARCHAR(255) NOT NULL,
        code VARCHAR(10) NOT NULL,
        type VARCHAR(20) DEFAULT 'login',
        expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
        used BOOLEAN DEFAULT FALSE,
        used_at TIMESTAMP WITH TIME ZONE,
        attempts INTEGER DEFAULT 0,
        ip_address INET,
        user_agent TEXT,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
      );`,
      
      // 用户会话表
      `CREATE TABLE IF NOT EXISTS user_sessions (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        user_id UUID REFERENCES users(id) ON DELETE CASCADE,
        session_token VARCHAR(128) NOT NULL UNIQUE,
        expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
        ip_address INET,
        user_agent TEXT,
        last_activity TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
      );`,
      
      // 登录日志表
      `CREATE TABLE IF NOT EXISTS login_logs (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        user_id UUID REFERENCES users(id) ON DELETE SET NULL,
        email VARCHAR(255),
        auth_type VARCHAR(50),
        status VARCHAR(20),
        ip_address INET,
        user_agent TEXT,
        error_message TEXT,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
      );`
    ]
    
    for (const sql of tables) {
      console.log(`📋 执行: ${sql.split('(')[0]}...`)
      const { error: tableError } = await supabase.sql`${sql}`
      if (tableError) {
        console.error('❌ 创建表失败:', tableError)
        throw tableError
      }
    }

    console.log('📑 创建索引...')
    // 创建索引
    const indexes = [
      'CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);',
      'CREATE INDEX IF NOT EXISTS idx_users_auth_type ON users(auth_type);', 
      'CREATE INDEX IF NOT EXISTS idx_users_has_password ON users(has_password);',
      'CREATE INDEX IF NOT EXISTS idx_email_codes_email ON email_codes(email);',
      'CREATE INDEX IF NOT EXISTS idx_email_codes_code ON email_codes(code);',
      'CREATE INDEX IF NOT EXISTS idx_email_codes_type ON email_codes(type);',
      'CREATE INDEX IF NOT EXISTS idx_email_codes_expires_at ON email_codes(expires_at);',
      'CREATE INDEX IF NOT EXISTS idx_email_codes_used ON email_codes(used);',
      'CREATE INDEX IF NOT EXISTS idx_user_sessions_user_id ON user_sessions(user_id);',
      'CREATE INDEX IF NOT EXISTS idx_user_sessions_token ON user_sessions(session_token);',
      'CREATE INDEX IF NOT EXISTS idx_user_sessions_expires_at ON user_sessions(expires_at);',
      'CREATE INDEX IF NOT EXISTS idx_login_logs_user_id ON login_logs(user_id);',
      'CREATE INDEX IF NOT EXISTS idx_login_logs_email ON login_logs(email);',
      'CREATE INDEX IF NOT EXISTS idx_login_logs_auth_type ON login_logs(auth_type);',
      'CREATE INDEX IF NOT EXISTS idx_login_logs_created_at ON login_logs(created_at);'
    ]
    
    for (const sql of indexes) {
      const { error: indexError } = await supabase.sql`${sql}`
      if (indexError) {
        console.warn('⚠️ 创建索引失败:', indexError.message)
        // 索引创建失败不阻止主流程
      }
    }

    console.log('✅ 邮箱验证码认证系统数据表创建成功！')
    
    // 验证表是否创建成功
    console.log('🔍 验证表结构...')
    
    const tableNames = ['users', 'email_codes', 'user_sessions', 'login_logs']
    for (const tableName of tableNames) {
      const { data, error } = await supabase
        .from(tableName)
        .select('*')
        .limit(1)
      
      if (error) {
        console.error(`❌ 表 ${tableName} 验证失败:`, error.message)
      } else {
        console.log(`✅ 表 ${tableName} 创建成功`)
      }
    }
    
    console.log('🎉 数据库设置完成！现在可以使用验证码登录系统了。')
    
  } catch (error) {
    console.error('❌ 设置过程中出现错误:', error)
    process.exit(1)
  }
}

// 执行设置
setupEmailAuthTables()