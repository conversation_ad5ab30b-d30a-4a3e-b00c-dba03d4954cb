import { NextRequest, NextResponse } from "next/server"
import { verifyEmailCode, resetUserPassword } from "@/lib/enhanced-email-auth"
import { supabaseAdmin } from "@/lib/supabase"

/**
 * 重置密码 API
 * POST /api/auth/email/reset-password
 */
export async function POST(request: NextRequest) {
  try {
    const { email, code, newPassword } = await request.json()
    
    if (!email || !code || !newPassword) {
      return NextResponse.json({
        success: false,
        error: "请填写所有必要信息"
      }, { status: 400 })
    }
    
    console.log('🔍 密码重置请求:', { email })
    
    // 验证邮箱验证码
    const verifyResult = await verifyEmailCode(email, code, 'reset_password')
    
    if (!verifyResult.valid) {
      return NextResponse.json({
        success: false,
        error: verifyResult.error
      }, { status: 400 })
    }
    
    // 检查用户是否存在
    const { data: existingUser } = await supabaseAdmin
      .from('users')
      .select('id')
      .eq('email', email)
      .single()
    
    if (!existingUser) {
      return NextResponse.json({
        success: false,
        error: "用户不存在"
      }, { status: 400 })
    }
    
    // 重置密码
    const resetResult = await resetUserPassword(email, newPassword)
    
    if (!resetResult.success) {
      return NextResponse.json({
        success: false,
        error: resetResult.error
      }, { status: 400 })
    }
    
    // 记录密码重置日志
    await supabaseAdmin
      .from('login_logs')
      .insert({
        user_id: existingUser.id,
        email,
        auth_type: 'password_reset',
        status: 'success',
        ip_address: request.ip || 'unknown',
        user_agent: request.headers.get('user-agent') || 'unknown',
        created_at: new Date().toISOString()
      })
    
    console.log('✅ 密码重置成功:', { 
      email, 
      userId: existingUser.id
    })
    
    return NextResponse.json({
      success: true,
      message: "密码重置成功"
    })
    
  } catch (error) {
    console.error('❌ 密码重置异常:', error)
    
    return NextResponse.json({
      success: false,
      error: "密码重置过程出现错误"
    }, { status: 500 })
  }
}