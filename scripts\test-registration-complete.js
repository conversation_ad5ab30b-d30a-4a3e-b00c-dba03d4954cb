require('dotenv').config({ path: '.env.local' });

async function testCompleteRegistrationFeatures() {
  const testEmail = `test+${Date.now()}@example.com`;
  console.log('🧪 完整注册功能测试...');
  console.log('测试邮箱:', testEmail);
  
  try {
    // 测试1: 正常注册流程
    console.log('\n1. 测试正常注册流程...');
    
    // 发送验证码
    const sendCodeResponse = await fetch('http://localhost:3001/api/auth/email/send-code', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ email: testEmail, type: 'register' })
    });
    
    const sendCodeData = await sendCodeResponse.json();
    if (!sendCodeData.success) {
      console.error('❌ 发送验证码失败:', sendCodeData.error);
      return;
    }
    
    console.log('✅ 验证码发送成功');
    const verifyCode = sendCodeData.code;
    
    // 执行注册
    const registerResponse = await fetch('http://localhost:3001/api/auth/email/register', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        email: testEmail,
        code: verifyCode,
        password: 'testpass123'
      })
    });
    
    const registerData = await registerResponse.json();
    if (!registerData.success) {
      console.error('❌ 注册失败:', registerData.error);
      return;
    }
    
    console.log('✅ 首次注册成功');
    console.log('- 用户ID:', registerData.user.id);
    console.log('- 邮箱:', registerData.user.email);
    console.log('- 用户名:', registerData.user.username);
    console.log('- 昵称:', registerData.user.nickname);
    console.log('- 有密码:', registerData.user.has_password);
    
    // 测试2: 重复注册保护
    console.log('\n2. 测试重复注册保护...');
    
    // 尝试为相同邮箱发送注册验证码
    const duplicateCodeResponse = await fetch('http://localhost:3001/api/auth/email/send-code', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ email: testEmail, type: 'register' })
    });
    
    const duplicateCodeData = await duplicateCodeResponse.json();
    
    if (!duplicateCodeData.success && duplicateCodeData.error.includes('已被注册')) {
      console.log('✅ 发送验证码阶段的重复注册保护正常工作');
    } else {
      console.warn('⚠️ 发送验证码阶段的重复注册保护可能有问题:', duplicateCodeData);
    }
    
    // 尝试用其他验证码注册相同邮箱（如果验证码发送成功的话）
    if (duplicateCodeData.success) {
      const duplicateRegisterResponse = await fetch('http://localhost:3001/api/auth/email/register', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          email: testEmail,
          code: duplicateCodeData.code,
          password: 'newpass123'
        })
      });
      
      const duplicateRegisterData = await duplicateRegisterResponse.json();
      
      if (!duplicateRegisterData.success && duplicateRegisterData.error.includes('已被注册')) {
        console.log('✅ 注册API阶段的重复注册保护正常工作');
      } else {
        console.warn('⚠️ 注册API阶段的重复注册保护可能有问题:', duplicateRegisterData);
      }
    }
    
    // 测试3: 无效验证码保护
    console.log('\n3. 测试无效验证码保护...');
    
    const newTestEmail = `test+${Date.now() + 1}@example.com`;
    const invalidRegisterResponse = await fetch('http://localhost:3001/api/auth/email/register', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        email: newTestEmail,
        code: '999999',
        password: 'testpass123'
      })
    });
    
    const invalidRegisterData = await invalidRegisterResponse.json();
    
    if (!invalidRegisterData.success && invalidRegisterData.error.includes('验证码')) {
      console.log('✅ 无效验证码保护正常工作');
    } else {
      console.warn('⚠️ 无效验证码保护可能有问题:', invalidRegisterData);
    }
    
    console.log('\n🎉 所有注册功能测试完成！');
    
  } catch (error) {
    console.error('❌ 测试过程中发生错误:', error.message);
  }
}

testCompleteRegistrationFeatures();