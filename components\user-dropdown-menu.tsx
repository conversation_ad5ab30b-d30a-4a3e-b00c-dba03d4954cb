"use client"

import { useState } from "react"
import { 
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,  
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Button } from "@/components/ui/button"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { 
  MessageCircle,
  Edit3,
  LogOut,
  User,
  ChevronDown
} from "lucide-react"
import { ChangeUsernameDialog } from "./change-username-dialog"

interface UserDropdownMenuProps {
  user: {
    id: string
    email: string
    username?: string
    nickname?: string
    avatar_url?: string
  }
  onUserUpdate: (updatedUser: any) => void
  onLogout: () => void
}

export function UserDropdownMenu({ user, onUserUpdate, onLogout }: UserDropdownMenuProps) {
  const [showChangeUsername, setShowChangeUsername] = useState(false)
  
  const displayName = user.nickname || user.username || '用户'
  const userInitial = displayName.charAt(0).toUpperCase()

  const handleUsernameChange = (updatedUser: any) => {
    onUserUpdate(updatedUser)
    setShowChangeUsername(false)
  }

  return (
    <>
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button 
            variant="ghost" 
            size="sm"
            className="flex items-center space-x-2 px-3 py-2 h-auto rounded-lg border border-green-200 bg-green-50 text-green-700 hover:bg-green-100 transition-colors"
          >
            {/* 用户头像 */}
            <Avatar className="h-6 w-6">
              <AvatarImage src={user.avatar_url} alt={displayName} />
              <AvatarFallback className="text-xs bg-green-200 text-green-800">
                {userInitial}
              </AvatarFallback>
            </Avatar>
            
            {/* 用户名称 - 桌面端显示 */}
            <span className="hidden sm:inline font-medium max-w-24 truncate">
              {displayName}
            </span>
            
            {/* 下拉箭头 */}
            <ChevronDown className="h-3 w-3 opacity-50" />
          </Button>
        </DropdownMenuTrigger>
        
        <DropdownMenuContent 
          align="end" 
          className="w-48 bg-white border shadow-lg rounded-lg"
        >
          {/* 用户信息头部 */}
          <div className="px-3 py-2 border-b">
            <div className="flex items-center space-x-2">
              <Avatar className="h-8 w-8">
                <AvatarImage src={user.avatar_url} alt={displayName} />
                <AvatarFallback className="text-sm bg-green-200 text-green-800">
                  {userInitial}
                </AvatarFallback>
              </Avatar>
              <div className="flex-1 min-w-0">
                <p className="text-sm font-medium text-gray-900 truncate">
                  {displayName}
                </p>
                <p className="text-xs text-gray-500 truncate">
                  {user.email}
                </p>
              </div>
            </div>
          </div>
          
          <DropdownMenuSeparator />
          
          {/* 菜单项 */}
          <DropdownMenuItem 
            onClick={() => setShowChangeUsername(true)}
            className="flex items-center px-3 py-2 text-sm text-gray-700 hover:bg-gray-50 cursor-pointer"
          >
            <Edit3 className="h-4 w-4 mr-2 text-gray-500" />
            更改用户名称
          </DropdownMenuItem>
          
          <DropdownMenuSeparator />
          
          <DropdownMenuItem 
            onClick={onLogout}
            className="flex items-center px-3 py-2 text-sm text-red-600 hover:bg-red-50 cursor-pointer"
          >
            <LogOut className="h-4 w-4 mr-2" />
            退出登录
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>

      {/* 更改用户名称对话框 */}
      <ChangeUsernameDialog
        open={showChangeUsername}
        onOpenChange={setShowChangeUsername}
        user={user}
        onSuccess={handleUsernameChange}
      />
    </>
  )
}