import { NextRequest, NextResponse } from "next/server"
import { supabaseAdmin } from "@/lib/supabase"

/**
 * 更新用户资料 API
 * PUT /api/auth/update-profile
 */
export async function PUT(request: NextRequest) {
  try {
    const { nickname } = await request.json()
    
    if (!nickname) {
      return NextResponse.json({
        success: false,
        error: "请提供用户名称"
      }, { status: 400 })
    }

    // 从Cookie中获取会话令牌
    const sessionToken = request.cookies.get('session_token')?.value
    
    if (!sessionToken) {
      return NextResponse.json({
        success: false,
        error: "会话无效或已过期，请重新登录"
      }, { status: 401 })
    }
    
    // 验证用户名称格式
    if (nickname.length < 2 || nickname.length > 20) {
      return NextResponse.json({
        success: false,
        error: "用户名称长度应为2-20个字符"
      }, { status: 400 })
    }
    
    // 检查是否包含敏感词或特殊字符
    const invalidChars = /[<>\"'&{}]/g
    if (invalidChars.test(nickname)) {
      return NextResponse.json({
        success: false,
        error: "用户名称包含无效字符"
      }, { status: 400 })
    }
    
    console.log('🔍 更新用户资料请求:', { nickname, sessionToken: sessionToken.substring(0, 8) + '...' })
    
    // 验证会话令牌并获取用户信息 - 使用数据库时间验证
    const { data: session, error: sessionError } = await supabaseAdmin
      .from('user_sessions')
      .select('user_id, expires_at, created_at')
      .eq('session_token', sessionToken)
      .single()
    
    if (sessionError || !session) {
      console.log('❌ 会话不存在:', sessionError?.message)
      return NextResponse.json({
        success: false,
        error: "会话无效或已过期，请重新登录"
      }, { status: 401 })
    }
    
    // 使用数据库查询来检查会话是否过期（避免时区问题）
    const { data: timeCheck, error: timeError } = await supabaseAdmin
      .from('user_sessions')
      .select('id')
      .eq('session_token', sessionToken)
      .gte('expires_at', new Date().toISOString()) // 数据库会用自己的时间比较
      .single()
    
    if (timeError || !timeCheck) {
      console.log('❌ 会话已过期:', { 
        expiresAt: session.expires_at,
        timeError: timeError?.message 
      })
      
      // 删除过期会话
      await supabaseAdmin
        .from('user_sessions')
        .delete()
        .eq('session_token', sessionToken)
      
      return NextResponse.json({
        success: false,
        error: "会话已过期，请重新登录"
      }, { status: 401 })
    }
    
    // 更新用户昵称
    const { data: updatedUser, error: updateError } = await supabaseAdmin
      .from('users')
      .update({ 
        nickname: nickname.trim(),
        updated_at: new Date().toISOString()
      })
      .eq('id', session.user_id)
      .select('id, email, username, nickname, avatar_url, auth_type, email_verified, has_password, created_at')
      .single()
    
    if (updateError) {
      console.error('❌ 更新用户资料失败:', updateError)
      return NextResponse.json({
        success: false,
        error: "更新失败，请稍后重试"
      }, { status: 500 })
    }
    
    // 更新会话活动时间 - 使用数据库时间
    await supabaseAdmin
      .from('user_sessions')
      .update({ 
        last_activity: new Date().toISOString() // 这个可以用服务器时间，因为只是记录活动
      })
      .eq('session_token', sessionToken)
    
    console.log('✅ 用户资料更新成功:', { 
      userId: session.user_id, 
      oldNickname: '***',
      newNickname: nickname 
    })
    
    return NextResponse.json({
      success: true,
      message: "用户名称更新成功",
      user: updatedUser
    })
    
  } catch (error) {
    console.error('❌ 更新用户资料异常:', error)
    
    return NextResponse.json({
      success: false,
      error: "请求处理失败，请稍后重试"
    }, { status: 500 })
  }
}

/**
 * 获取当前用户资料
 * GET /api/auth/update-profile?sessionToken=xxx
 */
export async function GET(request: NextRequest) {
  try {
    const searchParams = request.nextUrl.searchParams
    const sessionToken = searchParams.get('sessionToken')
    
    if (!sessionToken) {
      return NextResponse.json({
        success: false,
        error: "请提供会话令牌"
      }, { status: 400 })
    }
    
    // 验证会话令牌并获取用户信息
    const { data: session, error: sessionError } = await supabaseAdmin
      .from('user_sessions')
      .select('user_id, expires_at')
      .eq('session_token', sessionToken)
      .gte('expires_at', new Date().toISOString())
      .single()
    
    if (sessionError || !session) {
      return NextResponse.json({
        success: false,
        error: "会话无效或已过期"
      }, { status: 401 })
    }
    
    // 获取用户详细信息
    const { data: user, error: userError } = await supabaseAdmin
      .from('users')
      .select('id, email, username, nickname, avatar_url, auth_type, email_verified, has_password, created_at, last_login_at')
      .eq('id', session.user_id)
      .single()
    
    if (userError || !user) {
      return NextResponse.json({
        success: false,
        error: "用户不存在"
      }, { status: 404 })
    }
    
    return NextResponse.json({
      success: true,
      user
    })
    
  } catch (error) {
    console.error('❌ 获取用户资料异常:', error)
    
    return NextResponse.json({
      success: false,
      error: "请求处理失败"
    }, { status: 500 })
  }
}