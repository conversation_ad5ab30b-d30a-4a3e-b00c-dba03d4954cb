"use client"

import { useState, useEffect, useRef } from "react"
import { <PERSON><PERSON>, DialogContent } from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { 
  Mail, 
  Eye, 
  EyeOff, 
  RefreshCw,
  AlertCircle,
  CheckCircle
} from "lucide-react"
import { useToast } from "@/hooks/use-toast"

interface SimpleEmailLoginDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  onLoginSuccess: (userInfo: any) => void
}

export function SimpleEmailLoginDialog({ open, onOpenChange, onLoginSuccess }: SimpleEmailLoginDialogProps) {
  // 表单状态
  const [email, setEmail] = useState("")
  const [password, setPassword] = useState("")
  const [confirmPassword, setConfirmPassword] = useState("")
  const [emailCode, setEmailCode] = useState("")
  const [captchaCode, setCaptchaCode] = useState("")
  const [showPassword, setShowPassword] = useState(false)
  const [showConfirmPassword, setShowConfirmPassword] = useState(false)
  
  // 验证码状态
  const [emailCodeSent, setEmailCodeSent] = useState(false)
  const [emailCountdown, setEmailCountdown] = useState(0)
  const [captchaSession, setCaptchaSession] = useState("")
  
  // 加载状态
  const [loading, setLoading] = useState(false)
  const [sendingEmailCode, setSendingEmailCode] = useState(false)
  
  // 模式状态: 'login' | 'register' | 'reset'
  const [mode, setMode] = useState<'login' | 'register' | 'reset'>('login')
  
  // 图形验证码
  const captchaRef = useRef<HTMLImageElement>(null)
  
  const { toast } = useToast()

  // 验证邮箱格式
  const validateEmail = (email: string) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    return emailRegex.test(email)
  }

  // 验证密码强度
  const validatePassword = (password: string) => {
    if (password.length < 6) {
      return { valid: false, error: '密码长度至少6位' }
    }
    return { valid: true }
  }

  // 加载图形验证码
  const loadCaptcha = async () => {
    try {
      const response = await fetch('/api/captcha/generate', {
        method: 'GET',
        cache: 'no-cache'
      })
      
      if (response.ok) {
        const sessionId = response.headers.get('X-Captcha-Session')
        if (sessionId) {
          setCaptchaSession(sessionId)
        }
        
        const svgBlob = await response.blob()
        const url = URL.createObjectURL(svgBlob)
        
        if (captchaRef.current) {
          captchaRef.current.src = url
        }
      }
    } catch (error) {
      console.error('加载验证码失败:', error)
    }
  }

  // 验证图形验证码
  const verifyCaptcha = async (): Promise<boolean> => {
    if (!captchaCode || !captchaSession) {
      toast({
        title: "请输入图形验证码",
        variant: "destructive",
      })
      return false
    }
    
    try {
      const response = await fetch('/api/captcha/verify', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          sessionId: captchaSession,
          code: captchaCode
        })
      })
      
      const data = await response.json()
      
      if (data.success) {
        return true
      } else {
        toast({
          title: "图形验证码错误",
          description: data.error,
          variant: "destructive",
        })
        loadCaptcha() // 重新加载验证码
        setCaptchaCode("")
        return false
      }
    } catch (error) {
      console.error('验证图形验证码失败:', error)
      toast({
        title: "验证失败",
        description: "请重新尝试",
        variant: "destructive",
      })
      return false
    }
  }

  // 发送邮箱验证码
  const sendEmailCode = async () => {
    if (!email || !validateEmail(email)) {
      toast({
        title: "邮箱格式错误",
        description: "请输入正确的邮箱地址",
        variant: "destructive",
      })
      return
    }

    // 先验证图形验证码
    const captchaValid = await verifyCaptcha()
    if (!captchaValid) {
      return
    }

    setSendingEmailCode(true)
    
    try {
      const type = mode === 'register' ? 'register' : 
                   mode === 'reset' ? 'reset_password' : 'login'
      
      const response = await fetch('/api/auth/email/send-code', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email, type })
      })
      
      const data = await response.json()
      
      if (data.success) {
        setEmailCodeSent(true)
        setEmailCountdown(60)
        
        // 启动倒计时
        const timer = setInterval(() => {
          setEmailCountdown(prev => {
            if (prev <= 1) {
              clearInterval(timer)
              return 0
            }
            return prev - 1
          })
        }, 1000)
        
        toast({
          title: "验证码发送成功",
          description: "请查收您的邮箱",
        })
        
        // 重新加载图形验证码
        loadCaptcha()
        setCaptchaCode("")
      } else {
        toast({
          title: "发送失败",
          description: data.error || "请稍后重试",
          variant: "destructive",
        })
      }
    } catch (error) {
      console.error('发送邮箱验证码失败:', error)
      toast({
        title: "网络错误",
        description: "请检查网络连接后重试",
        variant: "destructive",
      })
    } finally {
      setSendingEmailCode(false)
    }
  }

  // 登录
  const handleLogin = async () => {
    if (!email || !validateEmail(email)) {
      toast({
        title: "请输入正确的邮箱地址",
        variant: "destructive",
      })
      return
    }

    // 验证码登录
    if (!emailCode) {
      toast({
        title: "请输入邮箱验证码",
        variant: "destructive",
      })
      return
    }

    setLoading(true)
    
    try {
      const response = await fetch('/api/auth/email/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ 
          email, 
          code: emailCode, 
          loginType: 'code' 
        })
      })
      
      const data = await response.json()
      
      if (data.success) {
        onLoginSuccess(data.user)
        onOpenChange(false)
        toast({
          title: "登录成功",
          description: `欢迎 ${data.user.nickname || data.user.username}！`,
        })
      } else {
        toast({
          title: "登录失败",
          description: data.error || "验证码错误",
          variant: "destructive",
        })
      }
    } catch (error) {
      console.error('登录失败:', error)
      toast({
        title: "登录异常",
        description: "请重新尝试",
        variant: "destructive",
      })
    } finally {
      setLoading(false)
    }
  }

  // 注册（包含密码设置）
  const handleRegister = async () => {
    if (!email || !validateEmail(email)) {
      toast({
        title: "请输入正确的邮箱地址",
        variant: "destructive",
      })
      return
    }

    if (!emailCode) {
      toast({
        title: "请输入邮箱验证码",
        variant: "destructive",
      })
      return
    }

    if (!password) {
      toast({
        title: "请设置密码",
        variant: "destructive",
      })
      return
    }

    const passwordValidation = validatePassword(password)
    if (!passwordValidation.valid) {
      toast({
        title: "密码格式错误",
        description: passwordValidation.error,
        variant: "destructive",
      })
      return
    }

    if (password !== confirmPassword) {
      toast({
        title: "密码不一致",
        description: "请确认两次输入的密码相同",
        variant: "destructive",
      })
      return
    }

    setLoading(true)
    
    try {
      // 先验证邮箱验证码并登录
      const loginResponse = await fetch('/api/auth/email/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ 
          email, 
          code: emailCode, 
          loginType: 'code' 
        })
      })
      
      const loginData = await loginResponse.json()
      
      if (loginData.success) {
        // 设置密码
        const passwordResponse = await fetch('/api/auth/email/password', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ 
            email, 
            code: emailCode, 
            password, 
            action: 'set' 
          })
        })
        
        const passwordData = await passwordResponse.json()
        
        if (passwordData.success) {
          onLoginSuccess({ ...loginData.user, has_password: true })
          onOpenChange(false)
          toast({
            title: "注册成功",
            description: "账户创建完成，欢迎使用知识商城！",
          })
        } else {
          toast({
            title: "密码设置失败",
            description: passwordData.error,
            variant: "destructive",
          })
        }
      } else {
        toast({
          title: "注册失败",
          description: loginData.error || "验证码错误",
          variant: "destructive",
        })
      }
    } catch (error) {
      console.error('注册失败:', error)
      toast({
        title: "注册异常",
        description: "请重新尝试",
        variant: "destructive",
      })
    } finally {
      setLoading(false)
    }
  }

  // 重置表单
  const resetForm = () => {
    setEmail("")
    setPassword("")
    setConfirmPassword("")
    setEmailCode("")
    setCaptchaCode("")
    setEmailCodeSent(false)
    setEmailCountdown(0)
    setShowPassword(false)
    setShowConfirmPassword(false)
    setLoading(false)
    setSendingEmailCode(false)
    setMode('login')
  }

  // 监听对话框打开
  useEffect(() => {
    if (open) {
      loadCaptcha()
    } else {
      resetForm()
    }
  }, [open])

  // 监听对话框关闭
  const handleOpenChange = (isOpen: boolean) => {
    if (!isOpen) {
      resetForm()
    }
    onOpenChange(isOpen)
  }

  return (
    <Dialog open={open} onOpenChange={handleOpenChange}>
      <DialogContent className="sm:max-w-md p-0 gap-0">
        {/* 简洁的顶部 */}
        <div className="px-6 py-4 border-b">
          <h2 className="text-xl font-semibold text-gray-900">
            {mode === 'login' ? '邮箱登录' : 
             mode === 'register' ? '邮箱注册' : '重置密码'}
          </h2>
        </div>

        <div className="px-6 py-6 space-y-4">
          {/* 邮箱地址 */}
          <div>
            <Input
              type="email"
              placeholder="邮箱地址"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              className="h-12 border-red-200 focus:border-red-400"
              disabled={loading || sendingEmailCode}
            />
            {!validateEmail(email) && email && (
              <p className="text-sm text-red-500 mt-1">请输入邮箱</p>
            )}
          </div>

          {/* 图形验证码 */}
          <div className="flex gap-3">
            <Input
              placeholder="请输入验证码"
              value={captchaCode}
              onChange={(e) => setCaptchaCode(e.target.value.toUpperCase())}
              className="flex-1 h-12"
              maxLength={4}
              disabled={loading || sendingEmailCode}
            />
            <div className="flex items-center gap-2">
              <img
                ref={captchaRef}
                className="h-12 w-24 border rounded cursor-pointer hover:opacity-80 transition-opacity"
                onClick={loadCaptcha}
                alt="图形验证码"
              />
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={loadCaptcha}
                className="h-12 px-3"
              >
                <RefreshCw className="h-4 w-4" />
              </Button>
            </div>
          </div>

          {/* 邮箱验证码 */}
          <div className="flex gap-3">
            <Input
              placeholder="请输入验证码"
              value={emailCode}
              onChange={(e) => setEmailCode(e.target.value.replace(/\D/g, '').slice(0, 6))}
              className="flex-1 h-12"
              maxLength={6}
              disabled={loading}
            />
            <Button
              type="button"
              variant="outline"
              onClick={sendEmailCode}
              disabled={sendingEmailCode || emailCountdown > 0 || !email || !captchaCode}
              className="h-12 px-4 min-w-[100px]"
            >
              {sendingEmailCode ? '发送中...' :
               emailCountdown > 0 ? `${emailCountdown}s` : '获取验证码'}
            </Button>
          </div>

          {/* 密码设置 - 仅注册和重置密码时显示 */}
          {(mode === 'register' || mode === 'reset') && (
            <>
              <div className="relative">
                <Input
                  type={showPassword ? "text" : "password"}
                  placeholder="设置密码"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  className="h-12 pr-10"
                  disabled={loading}
                />
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  className="absolute right-2 top-1/2 -translate-y-1/2 h-8 w-8 p-0"
                  onClick={() => setShowPassword(!showPassword)}
                >
                  {showPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                </Button>
              </div>

              <div className="relative">
                <Input
                  type={showConfirmPassword ? "text" : "password"}
                  placeholder="确认密码"
                  value={confirmPassword}
                  onChange={(e) => setConfirmPassword(e.target.value)}
                  className="h-12 pr-10"
                  disabled={loading}
                />
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  className="absolute right-2 top-1/2 -translate-y-1/2 h-8 w-8 p-0"
                  onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                >
                  {showConfirmPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                </Button>
              </div>
            </>
          )}

          {/* 协议说明 */}
          <p className="text-xs text-gray-500 leading-relaxed">
            登录视为您已同意开源项目UI设计和
            <span className="text-blue-600 cursor-pointer hover:underline">服务条款</span>
            和
            <span className="text-blue-600 cursor-pointer hover:underline">隐私政策</span>
          </p>

          {/* 主按钮 */}
          <Button
            onClick={mode === 'login' ? handleLogin : handleRegister}
            disabled={loading || !email || !emailCode || (mode !== 'login' && (!password || !confirmPassword))}
            className="w-full h-12 bg-blue-600 hover:bg-blue-700 text-white font-medium"
          >
            {loading ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                {mode === 'login' ? '登录中...' : 
                 mode === 'register' ? '注册中...' : '重置中...'}
              </>
            ) : (
              mode === 'login' ? '立即登录' : 
              mode === 'register' ? '立即注册' : '重置密码'
            )}
          </Button>

          {/* 切换模式 */}
          <div className="text-center space-x-4 text-sm">
            {mode !== 'register' && (
              <Button
                variant="link"
                className="text-blue-600 p-0 h-auto"
                onClick={() => setMode('register')}
              >
                IAM子用户登录
              </Button>
            )}
            <span className="text-gray-400">|</span>
            {mode !== 'login' && (
              <Button
                variant="link"
                className="text-blue-600 p-0 h-auto"
                onClick={() => setMode('login')}
              >
                企业联邦登录
              </Button>
            )}
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}