import { NextRequest, NextResponse } from "next/server"
import { generateCaptcha, createCaptchaSession, generateSessionId } from "@/lib/captcha"

/**
 * 生成图形验证码 API
 * GET /api/captcha/generate
 */
export async function GET(request: NextRequest) {
  try {
    // 生成会话ID
    const sessionId = generateSessionId()
    
    // 创建画布 (服务端模拟)
    const width = 120
    const height = 40
    
    // 生成验证码文本
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789'
    let code = ''
    for (let i = 0; i < 4; i++) {
      code += chars.charAt(Math.floor(Math.random() * chars.length))
    }
    
    console.log('🎨 生成图形验证码:', { sessionId, code })
    
    // 创建验证码会话
    createCaptchaSession(sessionId, code)
    
    // 生成SVG验证码 (更轻量，无需Canvas)
    const svg = generateSVGCaptcha(code, width, height)
    
    return new NextResponse(svg, {
      status: 200,
      headers: {
        'Content-Type': 'image/svg+xml',
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'X-Captcha-Session': sessionId, // 返回会话ID
      },
    })
  } catch (error) {
    console.error('❌ 生成验证码失败:', error)
    return NextResponse.json({
      success: false,
      error: "生成验证码失败"
    }, { status: 500 })
  }
}

// 生成SVG验证码
function generateSVGCaptcha(code: string, width: number, height: number): string {
  const colors = [
    '#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FECA57',
    '#FF9FF3', '#54A0FF', '#5F27CD', '#00D2D3', '#FF9F43'
  ]
  
  const getRandomColor = () => colors[Math.floor(Math.random() * colors.length)]
  
  let svgElements = []
  
  // 背景渐变
  svgElements.push(`
    <defs>
      <linearGradient id="bg" x1="0%" y1="0%" x2="100%" y2="100%">
        <stop offset="0%" style="stop-color:#f8fafc;stop-opacity:1" />
        <stop offset="100%" style="stop-color:#e2e8f0;stop-opacity:1" />
      </linearGradient>
    </defs>
    <rect width="${width}" height="${height}" fill="url(#bg)" />
  `)
  
  // 干扰线
  for (let i = 0; i < 5; i++) {
    const x1 = Math.random() * width
    const y1 = Math.random() * height
    const x2 = Math.random() * width
    const y2 = Math.random() * height
    svgElements.push(`
      <line x1="${x1}" y1="${y1}" x2="${x2}" y2="${y2}" 
            stroke="${getRandomColor()}" stroke-width="1" opacity="0.6"/>
    `)
  }
  
  // 验证码文字
  const charWidth = width / code.length
  for (let i = 0; i < code.length; i++) {
    const char = code[i]
    const x = charWidth * i + charWidth / 2
    const y = height / 2 + (Math.random() - 0.5) * 8
    const rotation = (Math.random() - 0.5) * 30
    const color = getRandomColor()
    
    svgElements.push(`
      <text x="${x}" y="${y}" 
            font-family="Arial, sans-serif" 
            font-size="24" 
            font-weight="bold"
            fill="${color}"
            text-anchor="middle"
            dominant-baseline="middle"
            transform="rotate(${rotation} ${x} ${y})"
            filter="drop-shadow(1px 1px 1px rgba(0,0,0,0.3))">
        ${char}
      </text>
    `)
  }
  
  // 干扰点
  for (let i = 0; i < 30; i++) {
    const x = Math.random() * width
    const y = Math.random() * height
    const r = Math.random() * 2 + 1
    svgElements.push(`
      <circle cx="${x}" cy="${y}" r="${r}" 
              fill="${getRandomColor()}" opacity="0.4"/>
    `)
  }
  
  return `
    <svg width="${width}" height="${height}" xmlns="http://www.w3.org/2000/svg">
      ${svgElements.join('')}
    </svg>
  `
}