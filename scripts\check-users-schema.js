require('dotenv').config({ path: '.env.local' });
const { createClient } = require('@supabase/supabase-js');

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ 缺少环境变量: NEXT_PUBLIC_SUPABASE_URL 或 SUPABASE_SERVICE_ROLE_KEY');
  process.exit(1);
}

const supabaseAdmin = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

async function checkUsersSchema() {
  try {
    console.log('🔍 检查users表结构...');
    
    // 尝试查询表结构信息
    const { data: existingUsers, error } = await supabaseAdmin
      .from('users')
      .select('*')
      .limit(1);
    
    if (error) {
      console.error('❌ 表查询错误:', error.message);
      return;
    }
    
    if (existingUsers && existingUsers.length > 0) {
      const fields = Object.keys(existingUsers[0]);
      console.log('✅ users表当前字段:', fields);
      
      // 检查是否缺少password相关字段
      const requiredFields = ['password_hash', 'has_password'];
      const missingFields = requiredFields.filter(field => !fields.includes(field));
      
      if (missingFields.length > 0) {
        console.log('⚠️ 缺少字段:', missingFields);
        
        // 检查是否可以添加字段
        console.log('\n🔧 准备添加缺少的字段...');
        
        for (const field of missingFields) {
          let query;
          if (field === 'password_hash') {
            query = `ALTER TABLE users ADD COLUMN password_hash text;`;
          } else if (field === 'has_password') {
            query = `ALTER TABLE users ADD COLUMN has_password boolean DEFAULT false;`;
          }
          
          console.log(`执行: ${query}`);
          const { error: alterError } = await supabaseAdmin.rpc('exec_sql', { sql_query: query });
          if (alterError) {
            console.error(`❌ 添加字段 ${field} 失败:`, alterError.message);
          } else {
            console.log(`✅ 成功添加字段: ${field}`);
          }
        }
      } else {
        console.log('✅ 所有必需字段都存在');
      }
    } else {
      console.log('ℹ️ users表为空，检查表结构定义...');
    }
    
    // 验证表结构
    const { data: finalCheck } = await supabaseAdmin
      .from('users')
      .select('*')
      .limit(1);
    
    if (finalCheck !== null) {
      console.log('\n✅ 最终验证 - users表字段:', finalCheck.length > 0 ? Object.keys(finalCheck[0]) : '表为空但结构正确');
    }
    
  } catch (err) {
    console.error('❌ 检查过程中发生错误:', err.message);
  }
}

checkUsersSchema();