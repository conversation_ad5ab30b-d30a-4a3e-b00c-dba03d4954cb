// 注册流程调试脚本
require('dotenv').config({ path: '.env.local' });
const { createClient } = require('@supabase/supabase-js');

const supabaseAdmin = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY,
  { auth: { autoRefreshToken: false, persistSession: false } }
);

async function debugRegistrationFlow() {
  console.log('🔍 调试注册流程问题...\n');
  
  try {
    // 1. 检查现有用户
    console.log('📊 1. 当前数据库用户状态');
    console.log('━'.repeat(50));
    
    const { data: users, error: userError } = await supabaseAdmin
      .from('users')
      .select('id, email, nickname, auth_type, created_at, last_login_at, login_count')
      .order('created_at', { ascending: false })
      .limit(10);
    
    if (userError) {
      console.error('❌ 查询用户失败:', userError.message);
    } else {
      console.log(`✅ 找到 ${users?.length || 0} 个用户`);
      users?.forEach((user, index) => {
        console.log(`\n  ${index + 1}. ${user.email}`);
        console.log(`     昵称: ${user.nickname}`);
        console.log(`     认证: ${user.auth_type}`);
        console.log(`     创建: ${user.created_at}`);
        console.log(`     登录: ${user.last_login_at || '从未'}`);
        console.log(`     次数: ${user.login_count || 0}`);
      });
    }
    
    // 2. 检查邮箱验证码记录
    console.log(`\n📊 2. 最近的邮箱验证码记录`);
    console.log('━'.repeat(50));
    
    const { data: codes, error: codeError } = await supabaseAdmin
      .from('email_codes')
      .select('email, code, type, used, expires_at, created_at')
      .order('created_at', { ascending: false })
      .limit(10);
    
    if (codeError) {
      console.error('❌ 查询验证码失败:', codeError.message);
    } else {
      console.log(`✅ 找到 ${codes?.length || 0} 条验证码记录`);
      codes?.forEach((code, index) => {
        const isExpired = new Date(code.expires_at) < new Date();
        console.log(`\n  ${index + 1}. ${code.email}`);
        console.log(`     验证码: ${code.code}`);
        console.log(`     类型: ${code.type}`);
        console.log(`     状态: ${code.used ? '已使用' : '未使用'}`);
        console.log(`     过期: ${isExpired ? '已过期' : '有效'}`);
        console.log(`     创建: ${code.created_at}`);
      });
    }
    
    // 3. 检查登录日志
    console.log(`\n📊 3. 最近的认证日志`);
    console.log('━'.repeat(50));
    
    const { data: logs, error: logError } = await supabaseAdmin
      .from('login_logs')
      .select('email, auth_type, status, error_message, created_at')
      .order('created_at', { ascending: false })
      .limit(15);
    
    if (logError) {
      console.error('❌ 查询日志失败:', logError.message);
    } else {
      console.log(`✅ 找到 ${logs?.length || 0} 条认证日志`);
      logs?.forEach((log, index) => {
        console.log(`\n  ${index + 1}. ${log.email}`);
        console.log(`     类型: ${log.auth_type}`);
        console.log(`     状态: ${log.status}`);
        console.log(`     时间: ${log.created_at}`);
        if (log.error_message) {
          console.log(`     错误: ${log.error_message}`);
        }
      });
    }
    
    // 4. 分析问题模式
    console.log(`\n🔍 4. 问题模式分析`);
    console.log('━'.repeat(50));
    
    // 查找最近注册尝试
    const recentRegisterCodes = codes?.filter(c => c.type === 'register') || [];
    const recentRegisterLogs = logs?.filter(l => l.auth_type === 'register' || l.auth_type === 'register_code_sent') || [];
    
    console.log(`📋 最近注册验证码: ${recentRegisterCodes.length} 个`);
    console.log(`📋 最近注册日志: ${recentRegisterLogs.length} 个`);
    
    if (recentRegisterCodes.length > 0) {
      console.log('\n最新的注册验证码:');
      const latestCode = recentRegisterCodes[0];
      console.log(`  邮箱: ${latestCode.email}`);
      console.log(`  验证码: ${latestCode.code}`);
      console.log(`  使用状态: ${latestCode.used ? '已使用' : '未使用'}`);
      
      // 检查该邮箱是否已有用户
      const { data: existingUser } = await supabaseAdmin
        .from('users')
        .select('id, created_at, auth_type')
        .eq('email', latestCode.email)
        .single();
      
      if (existingUser) {
        console.log(`  ⚠️ 该邮箱已存在用户: ${existingUser.id}`);
        console.log(`  用户创建时间: ${existingUser.created_at}`);
        console.log(`  认证类型: ${existingUser.auth_type}`);
        
        // 比较时间
        const codeTime = new Date(latestCode.created_at);
        const userTime = new Date(existingUser.created_at);
        if (userTime > codeTime) {
          console.log(`  🚨 用户创建时间晚于验证码，可能是验证码验证时创建的`);
        } else {
          console.log(`  ✅ 用户创建时间早于验证码，是正常情况`);
        }
      } else {
        console.log(`  ✅ 该邮箱尚未创建用户`);
      }
    }
    
  } catch (error) {
    console.error('❌ 调试过程异常:', error.message);
  }
}

// 执行调试
debugRegistrationFlow().then(() => {
  console.log('\n🎯 注册流程调试完成');
});