# 🗄️ Supabase数据库认证表结构创建指南

## 📋 操作步骤

### 1. 登录Supabase控制台
- 打开 https://supabase.com/dashboard
- 登录你的账户
- 选择项目：**tvkilvgjswuhigfkhyhf**

### 2. 打开SQL编辑器
- 在左侧菜单点击 **SQL Editor**
- 点击 **+ New Query** 创建新查询

### 3. 复制并执行以下SQL脚本

```sql
-- ==========================================
-- 邮箱魔法链接认证系统数据表
-- 知识商城项目专用
-- ==========================================

-- 1. 用户表 (扩展现有表结构)
CREATE TABLE IF NOT EXISTS users (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  email VARCHAR(255) UNIQUE,
  username VARCHAR(100),
  nickname VARCHAR(100),
  avatar_url TEXT,
  auth_type VARCHAR(50) DEFAULT 'email', -- email, wechat, phone, anonymous
  email_verified BOOLEAN DEFAULT FALSE,
  phone VARCHAR(20),
  phone_verified BOOLEAN DEFAULT FALSE,
  status VARCHAR(20) DEFAULT 'active', -- active, suspended, deleted
  login_count INTEGER DEFAULT 0,
  last_login_at TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 2. 魔法链接表 (核心表)
CREATE TABLE IF NOT EXISTS magic_links (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  email VARCHAR(255) NOT NULL,
  token VARCHAR(128) NOT NULL UNIQUE,
  signature VARCHAR(128) NOT NULL,
  expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
  used BOOLEAN DEFAULT FALSE,
  used_at TIMESTAMP WITH TIME ZONE,
  ip_address INET,
  user_agent TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 3. 手机验证码表 (备用功能)
CREATE TABLE IF NOT EXISTS phone_codes (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  phone VARCHAR(20) NOT NULL,
  code VARCHAR(10) NOT NULL,
  type VARCHAR(20) DEFAULT 'login', -- login, register, reset
  expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
  used BOOLEAN DEFAULT FALSE,
  used_at TIMESTAMP WITH TIME ZONE,
  attempts INTEGER DEFAULT 0,
  ip_address INET,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 4. 用户会话表
CREATE TABLE IF NOT EXISTS user_sessions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES users(id) ON DELETE CASCADE,
  session_token VARCHAR(128) NOT NULL UNIQUE,
  expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
  ip_address INET,
  user_agent TEXT,
  last_activity TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 5. 登录日志表
CREATE TABLE IF NOT EXISTS login_logs (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES users(id) ON DELETE SET NULL,
  email VARCHAR(255),
  auth_type VARCHAR(50), -- email, wechat, phone, anonymous
  status VARCHAR(20), -- success, failed, blocked
  ip_address INET,
  user_agent TEXT,
  error_message TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 6. 社交账号绑定表 (扩展功能)
CREATE TABLE IF NOT EXISTS social_accounts (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES users(id) ON DELETE CASCADE,
  provider VARCHAR(50) NOT NULL, -- wechat, qq, alipay
  provider_id VARCHAR(100) NOT NULL, -- openid, unionid
  provider_data JSONB, -- 存储额外的提供商数据
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(provider, provider_id)
);

-- 创建索引 (提升查询性能)
CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);
CREATE INDEX IF NOT EXISTS idx_users_phone ON users(phone);
CREATE INDEX IF NOT EXISTS idx_users_auth_type ON users(auth_type);

CREATE INDEX IF NOT EXISTS idx_magic_links_email ON magic_links(email);
CREATE INDEX IF NOT EXISTS idx_magic_links_token ON magic_links(token);
CREATE INDEX IF NOT EXISTS idx_magic_links_expires_at ON magic_links(expires_at);

CREATE INDEX IF NOT EXISTS idx_phone_codes_phone ON phone_codes(phone);
CREATE INDEX IF NOT EXISTS idx_phone_codes_expires_at ON phone_codes(expires_at);

CREATE INDEX IF NOT EXISTS idx_user_sessions_user_id ON user_sessions(user_id);
CREATE INDEX IF NOT EXISTS idx_user_sessions_token ON user_sessions(session_token);
CREATE INDEX IF NOT EXISTS idx_user_sessions_expires_at ON user_sessions(expires_at);

CREATE INDEX IF NOT EXISTS idx_login_logs_user_id ON login_logs(user_id);
CREATE INDEX IF NOT EXISTS idx_login_logs_created_at ON login_logs(created_at);

CREATE INDEX IF NOT EXISTS idx_social_accounts_user_id ON social_accounts(user_id);
CREATE INDEX IF NOT EXISTS idx_social_accounts_provider ON social_accounts(provider, provider_id);

-- 更新时间触发器
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_social_accounts_updated_at BEFORE UPDATE ON social_accounts
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- 清理过期数据的函数
CREATE OR REPLACE FUNCTION cleanup_expired_auth_data()
RETURNS INTEGER AS $$
DECLARE
    deleted_count INTEGER := 0;
BEGIN
    -- 清理过期的魔法链接
    DELETE FROM magic_links WHERE expires_at < NOW();
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    
    -- 清理过期的验证码
    DELETE FROM phone_codes WHERE expires_at < NOW();
    
    -- 清理过期的会话
    DELETE FROM user_sessions WHERE expires_at < NOW();
    
    -- 清理30天前的登录日志
    DELETE FROM login_logs WHERE created_at < NOW() - INTERVAL '30 days';
    
    RETURN deleted_count;
END;
$$ LANGUAGE plpgsql;

-- 添加注释
COMMENT ON TABLE users IS '用户基础信息表';
COMMENT ON TABLE magic_links IS '邮箱魔法链接表';
COMMENT ON TABLE phone_codes IS '手机验证码表';
COMMENT ON TABLE user_sessions IS '用户会话管理表';
COMMENT ON TABLE login_logs IS '登录日志表';
COMMENT ON TABLE social_accounts IS '社交账号绑定表';

COMMENT ON COLUMN users.auth_type IS '认证类型: email, wechat, phone, anonymous';
COMMENT ON COLUMN users.status IS '用户状态: active, suspended, deleted';
COMMENT ON COLUMN magic_links.signature IS 'HMAC签名，防止链接被篡改';
COMMENT ON COLUMN phone_codes.type IS '验证码类型: login, register, reset';
COMMENT ON COLUMN login_logs.status IS '登录状态: success, failed, blocked';
COMMENT ON COLUMN social_accounts.provider IS '社交平台: wechat, qq, alipay';
```

### 4. 执行SQL脚本
- 将上述SQL脚本完整复制到SQL编辑器中
- 点击右下角 **Run** 按钮执行
- 等待执行完成，查看是否有错误信息

### 5. 验证表创建结果
执行以下查询验证表是否创建成功：

```sql
-- 查看所有已创建的认证相关表
SELECT table_name 
FROM information_schema.tables 
WHERE table_schema = 'public' 
AND table_name IN (
    'users', 
    'magic_links', 
    'phone_codes', 
    'user_sessions', 
    'login_logs', 
    'social_accounts'
)
ORDER BY table_name;
```

### 6. 测试表结构
插入测试数据验证表结构：

```sql
-- 插入测试用户
INSERT INTO users (email, nickname, auth_type) 
VALUES ('<EMAIL>', '测试用户', 'email');

-- 查询用户表
SELECT * FROM users WHERE email = '<EMAIL>';

-- 清理测试数据
DELETE FROM users WHERE email = '<EMAIL>';
```

## 🔧 如果遇到问题

### 权限问题
如果遇到权限错误，确保你使用的是项目所有者账户登录。

### 表已存在
如果提示表已存在，可以先删除再重建：

```sql
-- 谨慎使用：删除现有表
DROP TABLE IF EXISTS social_accounts CASCADE;
DROP TABLE IF EXISTS login_logs CASCADE;
DROP TABLE IF EXISTS user_sessions CASCADE;
DROP TABLE IF EXISTS phone_codes CASCADE;
DROP TABLE IF EXISTS magic_links CASCADE;
DROP TABLE IF EXISTS users CASCADE;
```

### 分步执行
如果批量执行失败，可以分步执行：

1. 先执行表创建语句
2. 再执行索引创建语句  
3. 最后执行函数和触发器语句

## ✅ 完成确认

执行完成后，你应该看到以下6个表：
- ✅ `users` - 用户基础信息表
- ✅ `magic_links` - 魔法链接表
- ✅ `phone_codes` - 验证码表  
- ✅ `user_sessions` - 会话管理表
- ✅ `login_logs` - 登录日志表
- ✅ `social_accounts` - 社交账号表

完成后告诉我结果，我将帮你测试魔法链接登录功能！