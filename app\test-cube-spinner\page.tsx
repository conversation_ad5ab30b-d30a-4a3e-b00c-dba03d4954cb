'use client'

import { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { CubeSpinner } from '@/components/ui/cube-spinner'
import { useRouter } from 'next/navigation'
import { 
  Sparkles, 
  Palette,
  Eye,
  Play,
  ArrowLeft,
  Home,
  RefreshCw,
  Box
} from 'lucide-react'

export default function TestCubeSpinnerPage() {
  const router = useRouter()
  const [showDemo, setShowDemo] = useState(false)

  const handleDemo = () => {
    setShowDemo(true)
    // 模拟加载时间
    setTimeout(() => {
      setShowDemo(false)
    }, 3000)
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50 py-8">
      <div className="max-w-4xl mx-auto px-4">
        <div className="bg-white rounded-lg shadow-lg overflow-hidden">
          <div className="bg-gradient-to-r from-cyan-600 to-blue-600 text-white p-6">
            <h1 className="text-2xl font-bold flex items-center gap-2">
              <Box className="h-6 w-6" />
              3D方块加载动画升级
            </h1>
            <p className="mt-2 opacity-90">炫酷的3D旋转方块动画替换旧的转圈图标</p>
          </div>
          
          <div className="p-6 space-y-6">
            {/* 对比展示 */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Palette className="h-5 w-5 text-cyan-600" />
                  新旧动画对比
                </CardTitle>
                <CardDescription>
                  左侧为旧版2D转圈图标，右侧为新版3D方块旋转动画
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                  {/* 旧版动画 */}
                  <div className="text-center p-8 bg-gray-50 rounded-lg">
                    <h3 className="text-lg font-medium mb-4 text-gray-800">旧版2D图标</h3>
                    <div className="flex justify-center mb-4">
                      <RefreshCw className="h-16 w-16 text-blue-500 animate-spin" />
                    </div>
                    <p className="text-sm text-gray-600">简单的2D图标旋转</p>
                  </div>
                  
                  {/* 新版动画 */}
                  <div className="text-center p-8 bg-gradient-to-br from-cyan-50 to-blue-50 rounded-lg">
                    <h3 className="text-lg font-medium mb-4 text-gray-800">新版3D方块</h3>
                    <div className="flex justify-center mb-4">
                      <CubeSpinner size="lg" />
                    </div>
                    <p className="text-sm text-gray-600">立体3D方块旋转动画</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* 尺寸展示 */}
            <Card>
              <CardHeader>
                <CardTitle>不同尺寸展示</CardTitle>
                <CardDescription>
                  3D方块动画支持三种尺寸，适应不同使用场景
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  <div className="text-center p-6 bg-gray-50 rounded-lg">
                    <h4 className="font-medium mb-3">小型 (sm)</h4>
                    <div className="flex justify-center mb-2">
                      <CubeSpinner size="sm" />
                    </div>
                    <p className="text-xs text-gray-600">适用于按钮内加载</p>
                  </div>
                  
                  <div className="text-center p-6 bg-gray-50 rounded-lg">
                    <h4 className="font-medium mb-3">中型 (md)</h4>
                    <div className="flex justify-center mb-2">
                      <CubeSpinner size="md" />
                    </div>
                    <p className="text-xs text-gray-600">适用于组件区域加载</p>
                  </div>
                  
                  <div className="text-center p-6 bg-gray-50 rounded-lg">
                    <h4 className="font-medium mb-3">大型 (lg)</h4>
                    <div className="flex justify-center mb-2">
                      <CubeSpinner size="lg" />
                    </div>
                    <p className="text-xs text-gray-600">适用于页面级加载</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* 实际应用场景 */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Eye className="h-5 w-5 text-blue-600" />
                  实际应用场景
                </CardTitle>
                <CardDescription>
                  在实际使用场景中查看新动画效果
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                {/* 模拟按钮加载 */}
                <div className="p-4 bg-gray-50 rounded-lg">
                  <h4 className="font-medium mb-3">按钮加载状态</h4>
                  <div className="flex gap-3">
                    <Button disabled className="flex items-center gap-2">
                      <CubeSpinner size="sm" />
                      同步中...
                    </Button>
                    <Button variant="outline" disabled className="flex items-center gap-2">
                      <CubeSpinner size="sm" />
                      处理中...
                    </Button>
                  </div>
                </div>

                {/* 主页加载演示 */}
                <div className="p-4 bg-gray-50 rounded-lg">
                  <h4 className="font-medium mb-3">主页教程加载演示</h4>
                  <Button onClick={handleDemo} className="flex items-center gap-2">
                    <Play className="h-4 w-4" />
                    演示主页加载动画
                  </Button>
                  <p className="text-sm text-gray-600 mt-2">模拟主页教程数据加载过程</p>
                </div>

                {/* 真实主页链接 */}
                <div className="p-4 bg-gray-50 rounded-lg">
                  <h4 className="font-medium mb-3">查看真实主页加载</h4>
                  <Button 
                    onClick={() => router.push('/')} 
                    variant="outline" 
                    className="flex items-center gap-2"
                  >
                    <Home className="h-4 w-4" />
                    访问主页
                  </Button>
                  <p className="text-sm text-gray-600 mt-2">访问主页体验新的3D加载动画（刷新数据时显示）</p>
                </div>
              </CardContent>
            </Card>

            {/* 技术特性 */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Sparkles className="h-5 w-5 text-purple-600" />
                  技术特性
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-3">
                    <h4 className="font-medium">3D动画效果</h4>
                    <ul className="text-sm text-gray-600 space-y-1">
                      <li>• 真正的3D立体效果</li>
                      <li>• 多轴旋转动画</li>
                      <li>• 蓝色主题配色</li>
                      <li>• 平滑的动画过渡</li>
                    </ul>
                  </div>
                  <div className="space-y-3">
                    <h4 className="font-medium">技术实现</h4>
                    <ul className="text-sm text-gray-600 space-y-1">
                      <li>• CSS 3D Transform</li>
                      <li>• preserve-3d 空间</li>
                      <li>• 复合旋转动画</li>
                      <li>• 响应式尺寸设计</li>
                    </ul>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* 应用位置 */}
            <Card>
              <CardHeader>
                <CardTitle>已应用位置</CardTitle>
                <CardDescription>
                  新的3D方块动画已在以下位置替换了旧的2D图标
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex items-center gap-3 p-3 bg-green-50 rounded-lg">
                    <Home className="h-5 w-5 text-green-600" />
                    <div>
                      <div className="font-medium">主页教程加载</div>
                      <div className="text-sm text-gray-600">教程数据加载时的页面级3D方块动画</div>
                    </div>
                  </div>
                  <div className="flex items-center gap-3 p-3 bg-blue-50 rounded-lg">
                    <RefreshCw className="h-5 w-5 text-blue-600" />
                    <div>
                      <div className="font-medium">手动刷新按钮</div>
                      <div className="text-sm text-gray-600">保持原有的2D图标旋转动画</div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* 返回按钮 */}
            <div className="flex justify-center pt-4">
              <Button 
                onClick={() => router.push('/')}
                className="flex items-center gap-2"
                variant="outline"
              >
                <ArrowLeft className="h-4 w-4" />
                返回主页
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* 全屏演示模态框 */}
      {showDemo && (
        <div className="fixed inset-0 bg-gradient-to-br from-blue-50 via-white to-indigo-50 flex items-center justify-center z-50">
          <Card className="w-full max-w-md mx-auto shadow-lg border-slate-200">
            <CardContent className="flex flex-col items-center py-12">
              <CubeSpinner size="md" className="mb-6" />
              <p className="text-gray-500 text-lg">正在加载教程...</p>
              <p className="text-gray-400 text-center">请稍候</p>
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  )
}