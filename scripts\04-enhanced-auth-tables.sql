-- 邮箱验证码认证系统数据表
-- 支持验证码登录 + 密码设置功能

-- 1. 用户表 (扩展现有表结构)
CREATE TABLE IF NOT EXISTS users (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  email VARCHAR(255) UNIQUE,
  username VA<PERSON><PERSON><PERSON>(100),
  nickname <PERSON><PERSON><PERSON><PERSON>(100),
  avatar_url TEXT,
  auth_type VARCHAR(50) DEFAULT 'email', -- email, wechat, phone, anonymous
  email_verified BOOLEAN DEFAULT FALSE,
  phone VARCHAR(20),
  phone_verified BOOLEAN DEFAULT FALSE,
  -- 密码相关字段
  password_hash TEXT, -- bcrypt 哈希
  has_password BOOLEAN DEFAULT FALSE,
  password_updated_at TIMESTAMP WITH TIME ZONE,
  status VARCHAR(20) DEFAULT 'active', -- active, suspended, deleted
  login_count INTEGER DEFAULT 0,
  last_login_at TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 2. 邮箱验证码表
CREATE TABLE IF NOT EXISTS email_codes (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  email VARCHAR(255) NOT NULL,
  code VARCHAR(10) NOT NULL,
  type VARCHAR(20) DEFAULT 'login', -- login, register, reset_password, set_password
  expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
  used BOOLEAN DEFAULT FALSE,
  used_at TIMESTAMP WITH TIME ZONE,
  attempts INTEGER DEFAULT 0,
  ip_address INET,
  user_agent TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 3. 用户会话表
CREATE TABLE IF NOT EXISTS user_sessions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES users(id) ON DELETE CASCADE,
  session_token VARCHAR(128) NOT NULL UNIQUE,
  expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
  ip_address INET,
  user_agent TEXT,
  last_activity TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 4. 登录日志表
CREATE TABLE IF NOT EXISTS login_logs (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES users(id) ON DELETE SET NULL,
  email VARCHAR(255),
  auth_type VARCHAR(50), -- email_code, password_login, set_password, reset_password
  status VARCHAR(20), -- success, failed, blocked
  ip_address INET,
  user_agent TEXT,
  error_message TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 5. 密码历史表 (可选，用于防止重复使用旧密码)
CREATE TABLE IF NOT EXISTS password_history (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES users(id) ON DELETE CASCADE,
  password_hash TEXT NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 6. 社交账号绑定表 (保留备用)
CREATE TABLE IF NOT EXISTS social_accounts (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES users(id) ON DELETE CASCADE,
  provider VARCHAR(50) NOT NULL, -- wechat, qq, alipay
  provider_id VARCHAR(100) NOT NULL, -- openid, unionid
  provider_data JSONB, -- 存储额外的提供商数据
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(provider, provider_id)
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);
CREATE INDEX IF NOT EXISTS idx_users_phone ON users(phone);
CREATE INDEX IF NOT EXISTS idx_users_auth_type ON users(auth_type);
CREATE INDEX IF NOT EXISTS idx_users_has_password ON users(has_password);

CREATE INDEX IF NOT EXISTS idx_email_codes_email ON email_codes(email);
CREATE INDEX IF NOT EXISTS idx_email_codes_code ON email_codes(code);
CREATE INDEX IF NOT EXISTS idx_email_codes_type ON email_codes(type);
CREATE INDEX IF NOT EXISTS idx_email_codes_expires_at ON email_codes(expires_at);
CREATE INDEX IF NOT EXISTS idx_email_codes_used ON email_codes(used);

CREATE INDEX IF NOT EXISTS idx_user_sessions_user_id ON user_sessions(user_id);
CREATE INDEX IF NOT EXISTS idx_user_sessions_token ON user_sessions(session_token);
CREATE INDEX IF NOT EXISTS idx_user_sessions_expires_at ON user_sessions(expires_at);

CREATE INDEX IF NOT EXISTS idx_login_logs_user_id ON login_logs(user_id);
CREATE INDEX IF NOT EXISTS idx_login_logs_email ON login_logs(email);
CREATE INDEX IF NOT EXISTS idx_login_logs_auth_type ON login_logs(auth_type);
CREATE INDEX IF NOT EXISTS idx_login_logs_created_at ON login_logs(created_at);

CREATE INDEX IF NOT EXISTS idx_password_history_user_id ON password_history(user_id);
CREATE INDEX IF NOT EXISTS idx_password_history_created_at ON password_history(created_at);

CREATE INDEX IF NOT EXISTS idx_social_accounts_user_id ON social_accounts(user_id);
CREATE INDEX IF NOT EXISTS idx_social_accounts_provider ON social_accounts(provider, provider_id);

-- 更新时间触发器
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_social_accounts_updated_at BEFORE UPDATE ON social_accounts
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- 清理过期数据的函数
CREATE OR REPLACE FUNCTION cleanup_expired_auth_data()
RETURNS INTEGER AS $$
DECLARE
    deleted_count INTEGER := 0;
BEGIN
    -- 清理过期的邮箱验证码
    DELETE FROM email_codes WHERE expires_at < NOW();
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    
    -- 清理过期的会话
    DELETE FROM user_sessions WHERE expires_at < NOW();
    
    -- 清理30天前的登录日志
    DELETE FROM login_logs WHERE created_at < NOW() - INTERVAL '30 days';
    
    -- 清理90天前的密码历史
    DELETE FROM password_history WHERE created_at < NOW() - INTERVAL '90 days';
    
    RETURN deleted_count;
END;
$$ LANGUAGE plpgsql;

-- 密码安全检查函数
CREATE OR REPLACE FUNCTION check_password_reuse(
    p_user_id UUID,
    p_password_hash TEXT,
    p_history_limit INTEGER DEFAULT 5
)
RETURNS BOOLEAN AS $$
DECLARE
    is_reused BOOLEAN := FALSE;
BEGIN
    SELECT EXISTS(
        SELECT 1 FROM password_history 
        WHERE user_id = p_user_id 
        AND password_hash = p_password_hash
        ORDER BY created_at DESC
        LIMIT p_history_limit
    ) INTO is_reused;
    
    RETURN is_reused;
END;
$$ LANGUAGE plpgsql;

-- 添加密码到历史记录的函数
CREATE OR REPLACE FUNCTION add_password_to_history(
    p_user_id UUID,
    p_password_hash TEXT
)
RETURNS VOID AS $$
BEGIN
    -- 添加新密码到历史
    INSERT INTO password_history (user_id, password_hash, created_at)
    VALUES (p_user_id, p_password_hash, NOW());
    
    -- 只保留最近5次密码历史
    DELETE FROM password_history 
    WHERE user_id = p_user_id 
    AND id NOT IN (
        SELECT id FROM password_history 
        WHERE user_id = p_user_id 
        ORDER BY created_at DESC 
        LIMIT 5
    );
END;
$$ LANGUAGE plpgsql;

-- 添加注释
COMMENT ON TABLE users IS '用户基础信息表';
COMMENT ON TABLE email_codes IS '邮箱验证码表';
COMMENT ON TABLE user_sessions IS '用户会话管理表';
COMMENT ON TABLE login_logs IS '登录日志表';
COMMENT ON TABLE password_history IS '密码历史记录表';
COMMENT ON TABLE social_accounts IS '社交账号绑定表';

COMMENT ON COLUMN users.auth_type IS '认证类型: email, wechat, phone, anonymous';
COMMENT ON COLUMN users.status IS '用户状态: active, suspended, deleted';
COMMENT ON COLUMN users.has_password IS '是否设置了密码';
COMMENT ON COLUMN email_codes.type IS '验证码类型: login, register, reset_password, set_password';
COMMENT ON COLUMN login_logs.status IS '登录状态: success, failed, blocked';
COMMENT ON COLUMN social_accounts.provider IS '社交平台: wechat, qq, alipay';

-- 插入示例配置数据
INSERT INTO system_config (config_key, config_value, description) VALUES
('email_auth_enabled', 'true', '是否启用邮箱验证码认证'),
('password_auth_enabled', 'true', '是否启用密码认证'),
('code_expires_minutes', '10', '验证码有效期(分钟)'),
('max_code_attempts', '5', '验证码最大尝试次数'),
('max_codes_per_hour', '10', '每小时最大验证码请求次数'),
('password_min_length', '6', '密码最小长度'),
('password_history_limit', '5', '密码历史记录保留数量')
ON CONFLICT (config_key) DO UPDATE SET
    config_value = EXCLUDED.config_value,
    description = EXCLUDED.description,
    updated_at = NOW();