// 检查当前魔法链接数据和频率限制状态
const { createClient } = require('@supabase/supabase-js');
require('dotenv').config({ path: '.env.local' });

const supabaseAdmin = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY,
  {
    auth: {
      autoRefreshToken: false,
      persistSession: false
    }
  }
);

async function checkRateLimitIssue() {
  console.log('🔍 检查频率限制问题');
  console.log('=' .repeat(50));
  
  try {
    const testEmail = '<EMAIL>';
    
    // 1. 检查总的魔法链接记录
    console.log('📊 检查魔法链接记录...');
    const { data: allLinks, error: allError } = await supabaseAdmin
      .from('magic_links')
      .select('*')
      .eq('email', testEmail)
      .order('created_at', { ascending: false });
    
    if (allError) {
      console.error('❌ 查询失败:', allError.message);
      return;
    }
    
    console.log(`📈 总记录数: ${allLinks.length}`);
    
    if (allLinks.length > 0) {
      console.log('\\n📋 链接详情:');
      allLinks.forEach((link, index) => {
        const isExpired = new Date(link.expires_at) < new Date();
        const isUsed = link.used;
        const age = Math.round((Date.now() - new Date(link.created_at)) / 1000 / 60); // 分钟
        
        console.log(`${index + 1}. 创建: ${age}分钟前, 过期: ${isExpired ? '是' : '否'}, 已用: ${isUsed ? '是' : '否'}`);
      });
    }
    
    // 2. 检查1小时内的记录（当前限制逻辑）
    console.log('\\n⏰ 检查1小时内记录...');
    const oneHourAgo = new Date(Date.now() - 60 * 60 * 1000).toISOString();
    
    const { data: recentLinks, error: recentError } = await supabaseAdmin
      .from('magic_links')
      .select('*')
      .eq('email', testEmail)
      .gte('created_at', oneHourAgo);
    
    if (recentError) {
      console.error('❌ 查询失败:', recentError.message);
      return;
    }
    
    console.log(`📊 1小时内记录数: ${recentLinks.length}`);
    console.log(`🚦 当前限制: 5次/小时, 状态: ${recentLinks.length >= 5 ? '已触发' : '正常'}`);
    
    // 3. 分析问题
    console.log('\\n🔍 问题分析:');
    const activeLinks = recentLinks.filter(link => 
      !link.used && new Date(link.expires_at) > new Date()
    );
    
    console.log(`📌 有效未使用链接: ${activeLinks.length}`);
    console.log(`📌 已使用链接: ${recentLinks.filter(l => l.used).length}`);
    console.log(`📌 已过期链接: ${recentLinks.filter(l => new Date(l.expires_at) < new Date()).length}`);
    
    // 4. 提供解决方案
    console.log('\\n💡 解决方案:');
    if (recentLinks.length >= 5) {
      console.log('1. 清理测试数据');
      console.log('2. 优化频率限制逻辑（只计算有效链接）');
      console.log('3. 调整限制参数（增加允许次数或缩短时间窗口）');
    }
    
    if (allLinks.length > 0) {
      console.log('\\n🧹 是否清理测试数据？运行: node scripts/cleanup-test-data.js');
    }
    
  } catch (error) {
    console.error('❌ 检查过程异常:', error);
  }
}

checkRateLimitIssue().then(() => process.exit(0));