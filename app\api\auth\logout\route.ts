import { NextRequest, NextResponse } from "next/server"
import { supabaseAdmin } from "@/lib/supabase"

/**
 * 退出登录 API
 * POST /api/auth/logout
 */
export async function POST(request: NextRequest) {
  try {
    // 从Cookie中获取会话令牌
    const sessionToken = request.cookies.get('session_token')?.value
    
    if (!sessionToken) {
      // 即使没有会话令牌，也返回成功（可能已经过期或被删除）
      console.log('⚠️ 没有找到会话令牌，可能已经过期')
      const response = NextResponse.json({
        success: true,
        message: "退出登录成功"
      })
      
      // 清除会话Cookie
      response.cookies.set('session_token', '', {
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production',
        sameSite: 'lax',
        maxAge: 0,
        path: '/'
      })
      
      return response
    }
    
    console.log('🚪 退出登录请求:', { sessionToken: sessionToken.substring(0, 8) + '...' })
    
    // 查找并删除会话
    const { data: session, error: findError } = await supabaseAdmin
      .from('user_sessions')
      .select('user_id, expires_at')
      .eq('session_token', sessionToken)
      .single()
    
    if (findError || !session) {
      // 即使会话不存在，也返回成功（可能已经过期或被删除）
      console.log('⚠️ 会话不存在或已删除:', findError?.message)
      return NextResponse.json({
        success: true,
        message: "退出登录成功"
      })
    }
    
    // 删除会话记录
    const { error: deleteError } = await supabaseAdmin
      .from('user_sessions')
      .delete()
      .eq('session_token', sessionToken)
    
    if (deleteError) {
      console.error('❌ 删除会话失败:', deleteError)
      // 即使删除失败，也返回成功，因为客户端会清除本地状态
    }
    
    // 记录退出日志
    await supabaseAdmin
      .from('login_logs')
      .insert({
        user_id: session.user_id,
        auth_type: 'logout',
        status: 'success',
        ip_address: request.ip || 
                   request.headers.get("x-forwarded-for")?.split(',')[0] || 
                   "unknown",
        user_agent: request.headers.get('user-agent') || 'unknown',
        created_at: new Date().toISOString()
      })
    
    console.log('✅ 退出登录成功:', { userId: session.user_id })
    
    // 创建响应并清除Cookie
    const response = NextResponse.json({
      success: true,
      message: "退出登录成功"
    })
    
    // 清除会话Cookie
    response.cookies.set('session_token', '', {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'lax',
      maxAge: 0, // 立即过期
      path: '/'
    })
    
    return response
    
  } catch (error) {
    console.error('❌ 退出登录异常:', error)
    
    // 即使出现异常，也返回成功，让客户端清除本地状态
    const response = NextResponse.json({
      success: true,
      message: "退出登录成功"
    })
    
    // 清除Cookie
    response.cookies.set('session_token', '', {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'lax',
      maxAge: 0,
      path: '/'
    })
    
    return response
  }
}

/**
 * 退出所有设备的登录
 * DELETE /api/auth/logout
 */
export async function DELETE(request: NextRequest) {
  try {
    // 从Cookie中获取会话令牌
    const sessionToken = request.cookies.get('session_token')?.value
    
    if (!sessionToken) {
      return NextResponse.json({
        success: false,
        error: "未找到会话令牌"
      }, { status: 401 })
    }
    
    console.log('🚪 退出所有设备登录请求:', { sessionToken: sessionToken.substring(0, 8) + '...' })
    
    // 查找当前用户的会话
    const { data: currentSession, error: findError } = await supabaseAdmin
      .from('user_sessions')
      .select('user_id')
      .eq('session_token', sessionToken)
      .single()
    
    if (findError || !currentSession) {
      return NextResponse.json({
        success: false,
        error: "会话无效"
      }, { status: 401 })
    }
    
    // 删除该用户的所有会话
    const { error: deleteError } = await supabaseAdmin
      .from('user_sessions')
      .delete()
      .eq('user_id', currentSession.user_id)
    
    if (deleteError) {
      console.error('❌ 删除所有会话失败:', deleteError)
      return NextResponse.json({
        success: false,
        error: "退出失败，请稍后重试"
      }, { status: 500 })
    }
    
    // 记录退出所有设备日志
    await supabaseAdmin
      .from('login_logs')
      .insert({
        user_id: currentSession.user_id,
        auth_type: 'logout_all_devices',
        status: 'success',
        ip_address: request.ip || 
                   request.headers.get("x-forwarded-for")?.split(',')[0] || 
                   "unknown",
        user_agent: request.headers.get('user-agent') || 'unknown',
        created_at: new Date().toISOString()
      })
    
    console.log('✅ 退出所有设备成功:', { userId: currentSession.user_id })
    
    // 创建响应并清除Cookie
    const response = NextResponse.json({
      success: true,
      message: "已退出所有设备的登录"
    })
    
    // 清除会话Cookie
    response.cookies.set('session_token', '', {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'lax',
      maxAge: 0,
      path: '/'
    })
    
    return response
    
  } catch (error) {
    console.error('❌ 退出所有设备异常:', error)
    
    return NextResponse.json({
      success: false,
      error: "请求处理失败，请稍后重试"
    }, { status: 500 })
  }
}