#!/usr/bin/env node

/**
 * 🤖 SuperClaude 中文关键词Agent智能分配系统
 * 知识商城项目专用 - 自动检测中文关键词并推荐最佳Agent
 */

const fs = require('fs');
const path = require('path');

class ChineseKeywordAgentMatcher {
  constructor() {
    this.config = this.loadConfig();
    this.debugMode = process.env.DEBUG_AGENT_MATCHING === 'true';
  }

  loadConfig() {
    try {
      // 直接返回配置对象，无需解析文件
      return {
        // 前端开发类
        'frontend-developer': {
          keywords: {
            primary: ['组件', '界面', 'UI组件', 'React组件', '前端', '页面', '布局', '响应式', '样式', 'CSS', 'Tailwind', 'shadcn'],
            secondary: ['用户界面', '交互', '动画', '主题', '深色模式', '浅色模式', '移动端', '桌面端']
          },
          confidence: 0.9,
          priority: 8,
          autoFlags: ['--magic', '--c7']
        },
        // 后端架构类
        'backend-architect': {
          keywords: {
            primary: ['API', '接口', '后端', '数据库', '服务器', '路由', '中间件', '认证', '权限', 'PostgreSQL', 'Supabase'],
            secondary: ['数据模型', '数据表', '查询', '事务', '连接池', '缓存', '性能优化', '扩展性']
          },
          confidence: 0.95,
          priority: 9,
          autoFlags: ['--seq', '--c7']
        },
        // 安全审计类
        'security-auditor': {
          keywords: {
            primary: ['安全', '漏洞', '认证', '授权', '加密', '密钥', 'JWT', '哈希', '防护'],
            secondary: ['注入攻击', 'XSS', 'CSRF', 'SQL注入', '身份验证', '权限控制', '数据保护', '合规性']
          },
          confidence: 0.92,
          priority: 10,
          autoFlags: ['--seq', '--validate', '--safe-mode']
        },
        // 数据库优化类
        'database-optimizer': {
          keywords: {
            primary: ['数据库优化', '查询优化', '索引', '性能调优', 'SQL优化', '慢查询'],
            secondary: ['数据库设计', '表结构', '关联查询', '分页查询', '数据库连接', '查询计划']
          },
          confidence: 0.88,
          priority: 7,
          autoFlags: ['--seq', '--think']
        },
        // 性能工程师类
        'performance-engineer': {
          keywords: {
            primary: ['性能优化', '性能测试', '加载速度', '响应时间', '内存优化', '缓存'],
            secondary: ['首屏加载', '代码分割', '懒加载', '压缩', 'CDN', '监控', '指标']
          },
          confidence: 0.87,
          priority: 7,
          autoFlags: ['--play', '--think', '--validate']
        },
        // 错误侦探类
        'error-detective': {
          keywords: {
            primary: ['错误', 'Bug', '调试', '故障排除', '问题诊断', '日志分析'],
            secondary: ['异常处理', '错误追踪', '堆栈跟踪', '调试信息', '监控告警']
          },
          confidence: 0.90,
          priority: 8,
          autoFlags: ['--seq', '--think']
        },
        // 文档专家类
        'api-documenter': {
          keywords: {
            primary: ['文档', '说明', '注释', 'API文档', '用户手册', '开发文档'],
            secondary: ['README', '接口说明', '使用指南', '示例代码', '变更日志']
          },
          confidence: 0.80,
          priority: 5,
          autoFlags: ['--persona-scribe=zh', '--c7']
        },
        // 系统架构师类
        'architect': {
          keywords: {
            primary: ['架构', '系统设计', '模块化', '可扩展性', '设计模式', '重构'],
            secondary: ['技术选型', '架构决策', '系统集成', '微服务', '模块依赖', '代码结构']
          },
          confidence: 0.95,
          priority: 9,
          autoFlags: ['--seq', '--ultrathink', '--c7']
        },
        // 代码重构师类
        'refactorer': {
          keywords: {
            primary: ['重构', '代码优化', '代码清理', '技术债务', '代码质量'],
            secondary: ['代码规范', '最佳实践', '代码复用', '简化代码', '消除重复']
          },
          confidence: 0.85,
          priority: 6,
          autoFlags: ['--seq', '--validate']
        },
        // 测试自动化类
        'test-automator': {
          keywords: {
            primary: ['测试', '单元测试', '集成测试', '端到端测试', '自动化测试', '测试用例'],
            secondary: ['测试覆盖率', '测试框架', 'Mock', '断言', '测试数据', '回归测试']
          },
          confidence: 0.85,
          priority: 6,
          autoFlags: ['--play', '--validate']
        }
      };
    } catch (error) {
      console.error('❌ 配置文件加载失败:', error.message);
      return {};
    }
  }

  /**
   * 分析用户输入，匹配最佳Agent
   */
  analyzeAndRecommend(userInput, options = {}) {
    if (!userInput || typeof userInput !== 'string') {
      return this.getDefaultRecommendation();
    }

    const analysis = this.analyzeInput(userInput);
    const matches = this.findMatches(analysis);
    const bestMatch = this.selectBestMatch(matches);
    
    return this.formatRecommendation(bestMatch, analysis, options);
  }

  /**
   * 分析用户输入
   */
  analyzeInput(input) {
    const text = input.toLowerCase();
    return {
      originalText: input,
      cleanText: text,
      length: text.length,
      complexity: this.calculateComplexity(text),
      hasMultipleSteps: this.detectMultipleSteps(text),
      projectContext: this.detectProjectContext(text),
      urgency: this.detectUrgency(text)
    };
  }

  /**
   * 计算输入复杂度
   */
  calculateComplexity(text) {
    let complexity = 0;
    
    // 基于长度
    if (text.length > 100) complexity += 0.2;
    if (text.length > 300) complexity += 0.2;
    
    // 基于关键词
    const complexityKeywords = ['系统', '架构', '复杂', '全面', '综合', '多个', '批量'];
    complexityKeywords.forEach(keyword => {
      if (text.includes(keyword)) complexity += 0.15;
    });
    
    // 基于技术术语
    const techKeywords = ['数据库', 'API', '性能', '安全', '部署', '测试'];
    techKeywords.forEach(keyword => {
      if (text.includes(keyword)) complexity += 0.1;
    });
    
    return Math.min(complexity, 1.0);
  }

  /**
   * 检测多步骤任务
   */
  detectMultipleSteps(text) {
    const stepIndicators = ['首先', '然后', '接着', '最后', '步骤', '阶段', '依次', '逐步'];
    return stepIndicators.some(indicator => text.includes(indicator));
  }

  /**
   * 检测项目上下文
   */
  detectProjectContext(text) {
    const contexts = {
      tutorial: ['教程', '课程', '内容', '章节', '学习'],
      auth: ['登录', '注册', '认证', '权限', '用户'],
      admin: ['管理', '后台', '管理员', '控制台'],
      key: ['密钥', '解锁', '验证', '购买']
    };
    
    for (const [context, keywords] of Object.entries(contexts)) {
      if (keywords.some(keyword => text.includes(keyword))) {
        return context;
      }
    }
    return null;
  }

  /**
   * 检测紧急程度
   */
  detectUrgency(text) {
    const urgentKeywords = ['紧急', '立即', '马上', '尽快', '急需', 'bug', '错误', '故障'];
    return urgentKeywords.some(keyword => text.includes(keyword));
  }

  /**
   * 查找匹配的Agent
   */
  findMatches(analysis) {
    const matches = [];
    
    for (const [agentName, agentConfig] of Object.entries(this.config)) {
      const score = this.calculateMatchScore(analysis, agentConfig);
      if (this.debugMode) {
        console.log(`🔍 ${agentName}: score=${score.toFixed(3)}`);
      }
      if (score > 0.05) { // 降低最低匹配阈值
        matches.push({
          agent: agentName,
          score: score,
          confidence: agentConfig.confidence,
          priority: agentConfig.priority,
          autoFlags: agentConfig.autoFlags || [],
          reasons: this.getMatchReasons(analysis, agentConfig)
        });
      }
    }
    
    if (this.debugMode) {
      console.log(`🎯 找到 ${matches.length} 个匹配项`);
    }
    
    return matches.sort((a, b) => b.score - a.score);
  }

  /**
   * 计算匹配分数
   */
  calculateMatchScore(analysis, agentConfig) {
    let score = 0;
    const text = analysis.cleanText;
    
    // 主要关键词匹配 (权重 0.6)
    const primaryMatches = agentConfig.keywords.primary.filter(keyword => 
      text.includes(keyword.toLowerCase())
    );
    score += (primaryMatches.length / agentConfig.keywords.primary.length) * 0.6;
    
    // 次要关键词匹配 (权重 0.3)
    const secondaryMatches = agentConfig.keywords.secondary.filter(keyword => 
      text.includes(keyword.toLowerCase())
    );
    score += (secondaryMatches.length / agentConfig.keywords.secondary.length) * 0.3;
    
    // 复杂度匹配奖励 (权重 0.1)
    if (analysis.complexity > 0.5 && agentConfig.priority >= 8) {
      score += 0.1;
    }
    
    return Math.min(score, 1.0);
  }

  /**
   * 获取匹配原因
   */
  getMatchReasons(analysis, agentConfig) {
    const reasons = [];
    const text = analysis.cleanText;
    
    // 检查主要关键词
    const primaryMatches = agentConfig.keywords.primary.filter(keyword => 
      text.includes(keyword.toLowerCase())
    );
    if (primaryMatches.length > 0) {
      reasons.push(`匹配主要关键词: ${primaryMatches.join(', ')}`);
    }
    
    // 检查次要关键词
    const secondaryMatches = agentConfig.keywords.secondary.filter(keyword => 
      text.includes(keyword.toLowerCase())
    );
    if (secondaryMatches.length > 0) {
      reasons.push(`匹配次要关键词: ${secondaryMatches.slice(0, 3).join(', ')}`);
    }
    
    return reasons;
  }

  /**
   * 选择最佳匹配
   */
  selectBestMatch(matches) {
    if (matches.length === 0) {
      return null;
    }
    
    // 优先选择高分数且高优先级的Agent
    return matches.reduce((best, current) => {
      const bestScore = best.score * best.priority;
      const currentScore = current.score * current.priority;
      return currentScore > bestScore ? current : best;
    });
  }

  /**
   * 格式化推荐结果
   */
  formatRecommendation(bestMatch, analysis, options) {
    if (!bestMatch) {
      return this.getDefaultRecommendation();
    }

    const recommendation = {
      recommendedAgent: bestMatch.agent,
      confidence: Math.round(bestMatch.score * 100),
      priority: bestMatch.priority,
      autoFlags: bestMatch.autoFlags,
      reasons: bestMatch.reasons,
      analysis: {
        complexity: Math.round(analysis.complexity * 100),
        hasMultipleSteps: analysis.hasMultipleSteps,
        projectContext: analysis.projectContext,
        urgency: analysis.urgency
      },
      command: this.generateCommand(bestMatch, analysis, options),
      alternatives: this.getAlternatives(bestMatch, analysis)
    };

    if (this.debugMode) {
      console.log('🔍 调试信息:', JSON.stringify(recommendation, null, 2));
    }

    return recommendation;
  }

  /**
   * 生成推荐命令
   */
  generateCommand(bestMatch, analysis, options) {
    let command = `/Task`;
    
    // 添加自动标志
    if (bestMatch.autoFlags && bestMatch.autoFlags.length > 0) {
      command += ' ' + bestMatch.autoFlags.join(' ');
    }
    
    // 根据复杂度添加思考标志
    if (analysis.complexity > 0.7) {
      command += ' --ultrathink';
    } else if (analysis.complexity > 0.4) {
      command += ' --think';
    }
    
    // 如果是多步骤任务，添加循环标志
    if (analysis.hasMultipleSteps) {
      command += ' --loop';
    }
    
    // 如果是紧急任务，添加验证标志
    if (analysis.urgency) {
      command += ' --validate --safe-mode';
    }
    
    return command;
  }

  /**
   * 获取备选方案
   */
  getAlternatives(bestMatch, analysis) {
    // 返回前3个备选Agent
    const allMatches = Object.keys(this.config)
      .filter(agent => agent !== bestMatch.agent)
      .map(agent => ({
        agent: agent,
        score: this.calculateMatchScore(analysis, this.config[agent]) * this.config[agent].priority
      }))
      .sort((a, b) => b.score - a.score)
      .slice(0, 3);
    
    return allMatches.map(match => match.agent);
  }

  /**
   * 获取默认推荐
   */
  getDefaultRecommendation() {
    return {
      recommendedAgent: 'general-purpose',
      confidence: 50,
      priority: 5,
      autoFlags: [],
      reasons: ['使用默认通用代理'],
      analysis: {},
      command: '/Task',
      alternatives: ['frontend-developer', 'backend-architect', 'security-auditor']
    };
  }

  /**
   * 打印美化的推荐结果
   */
  printRecommendation(recommendation, userInput) {
    console.log('\n🤖 SuperClaude Agent 智能推荐系统');
    console.log('=' .repeat(50));
    console.log(`📝 用户输入: ${userInput.substring(0, 100)}${userInput.length > 100 ? '...' : ''}`);
    console.log(`🎯 推荐Agent: ${recommendation.recommendedAgent}`);
    console.log(`🎲 置信度: ${recommendation.confidence}%`);
    console.log(`⭐ 优先级: ${recommendation.priority}/10`);
    
    if (recommendation.autoFlags.length > 0) {
      console.log(`🚩 自动标志: ${recommendation.autoFlags.join(' ')}`);
    }
    
    if (recommendation.reasons.length > 0) {
      console.log(`💡 匹配原因:`);
      recommendation.reasons.forEach(reason => console.log(`   • ${reason}`));
    }
    
    console.log(`🔧 推荐命令: ${recommendation.command}`);
    
    if (recommendation.alternatives.length > 0) {
      console.log(`🔄 备选方案: ${recommendation.alternatives.join(', ')}`);
    }
    
    console.log('=' .repeat(50));
  }
}

// 导出供其他模块使用
module.exports = ChineseKeywordAgentMatcher;

// 命令行使用
if (require.main === module) {
  const matcher = new ChineseKeywordAgentMatcher();
  const userInput = process.argv.slice(2).join(' ');
  
  if (!userInput) {
    console.log('使用方法: node agent-matcher.js "你的中文输入"');
    console.log('示例: node agent-matcher.js "优化数据库查询性能"');
    process.exit(1);
  }
  
  const recommendation = matcher.analyzeAndRecommend(userInput);
  matcher.printRecommendation(recommendation, userInput);
}