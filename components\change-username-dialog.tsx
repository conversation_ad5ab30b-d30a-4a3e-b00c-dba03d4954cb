"use client"

import { useState } from "react"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>eader, DialogTitle } from "@/components/ui/dialog"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { 
  Edit3, 
  Check, 
  X, 
  Loader2,
  User,
  AlertCircle
} from "lucide-react"
import { useToast } from "@/hooks/use-toast"
import { cn } from "@/lib/utils"

interface ChangeUsernameDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  user: {
    id: string
    email: string
    username?: string
    nickname?: string
  }
  onSuccess: (updatedUser: any) => void
}

export function ChangeUsernameDialog({ open, onOpenChange, user, onSuccess }: ChangeUsernameDialogProps) {
  const [newNickname, setNewNickname] = useState(user.nickname || user.username || "")
  const [loading, setLoading] = useState(false)
  const [validationError, setV<PERSON>dationError] = useState("")
  
  const { toast } = useToast()

  // 实时验证用户名称
  const validateNickname = (nickname: string) => {
    if (!nickname.trim()) {
      return "用户名称不能为空"
    }
    if (nickname.length < 2) {
      return "用户名称至少2个字符"
    }
    if (nickname.length > 20) {
      return "用户名称不能超过20个字符"
    }
    const invalidChars = /[<>"'&{}]/g
    if (invalidChars.test(nickname)) {
      return "用户名称包含无效字符"
    }
    return ""
  }

  // 处理输入变化
  const handleInputChange = (value: string) => {
    setNewNickname(value)
    setValidationError(validateNickname(value))
  }

  // 提交更改
  const handleSubmit = async () => {
    const trimmedNickname = newNickname.trim()
    const error = validateNickname(trimmedNickname)
    
    if (error) {
      setValidationError(error)
      return
    }

    // 检查是否有变化
    if (trimmedNickname === (user.nickname || user.username)) {
      toast({
        title: "没有变化",
        description: "用户名称未发生改变",
        variant: "default",
      })
      return
    }

    setLoading(true)
    
    try {
      const response = await fetch('/api/auth/update-profile', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          nickname: trimmedNickname
        })
      })
      
      const data = await response.json()
      
      if (data.success) {
        // 更新本地存储的用户信息
        try {
          const savedUser = localStorage.getItem('userInfo')
          if (savedUser) {
            const userInfo = JSON.parse(savedUser)
            userInfo.nickname = trimmedNickname
            localStorage.setItem('userInfo', JSON.stringify(userInfo))
          }
        } catch (error) {
          console.error('更新本地用户信息失败:', error)
        }

        onSuccess(data.user)
        onOpenChange(false)
        
        toast({
          title: "更新成功",
          description: `用户名称已更改为"${trimmedNickname}"`,
        })
      } else {
        throw new Error(data.error || '更新失败')
      }
    } catch (error) {
      console.error('更改用户名称失败:', error)
      toast({
        title: "更新失败",
        description: error instanceof Error ? error.message : "请稍后重试",
        variant: "destructive",
      })
    } finally {
      setLoading(false)
    }
  }

  // 重置表单
  const handleOpenChange = (isOpen: boolean) => {
    if (!isOpen) {
      setNewNickname(user.nickname || user.username || "")
      setValidationError("")
      setLoading(false)
    }
    onOpenChange(isOpen)
  }

  const isValid = !validationError && newNickname.trim().length >= 2
  const hasChanges = newNickname.trim() !== (user.nickname || user.username)

  return (
    <Dialog open={open} onOpenChange={handleOpenChange}>
      <DialogContent className="sm:max-w-[420px] p-0 gap-0 overflow-hidden bg-white border-0 shadow-2xl">
        {/* 现代化背景装饰 */}
        <div className="absolute inset-0 bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50">
          <div className="absolute top-0 left-0 w-32 h-32 bg-gradient-to-br from-blue-400/20 to-purple-400/20 rounded-full blur-xl"></div>
          <div className="absolute bottom-0 right-0 w-40 h-40 bg-gradient-to-tl from-indigo-400/20 to-pink-400/20 rounded-full blur-xl"></div>
        </div>
        
        {/* 内容区域 */}
        <div className="relative backdrop-blur-sm bg-white/80 p-6 space-y-6">
          {/* 标题区域 */}
          <DialogHeader className="text-center space-y-2">
            <div className="flex items-center justify-center mb-2">
              <div className="p-3 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full shadow-lg">
                <Edit3 className="h-5 w-5 text-white" />
              </div>
            </div>
            <DialogTitle className="text-xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
              更改用户名称
            </DialogTitle>
            <p className="text-gray-600 text-sm">设置一个更符合你的个性化名称</p>
          </DialogHeader>

          <div className="space-y-4">
            {/* 当前用户信息显示 */}
            <div className="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg border">
              <div className="p-2 bg-blue-100 rounded-full">
                <User className="h-4 w-4 text-blue-600" />
              </div>
              <div className="flex-1 min-w-0">
                <p className="text-sm font-medium text-gray-900">当前名称</p>
                <p className="text-sm text-gray-600 truncate">
                  {user.nickname || user.username || '未设置'}
                </p>
              </div>
            </div>

            {/* 新用户名称输入 */}
            <div className="space-y-2">
              <Label htmlFor="nickname" className="text-sm font-medium text-gray-700">
                新用户名称
              </Label>
              <div className="relative">
                <Input
                  id="nickname"
                  type="text"
                  placeholder="输入新的用户名称"
                  value={newNickname}
                  onChange={(e) => handleInputChange(e.target.value)}
                  className={cn(
                    "h-12 pl-12 pr-10 bg-white/60 backdrop-blur-sm border-2 transition-all duration-300 rounded-xl",
                    validationError ? "border-red-300 focus:border-red-500" :
                    isValid ? "border-green-300 focus:border-green-500" :
                    "border-gray-200 focus:border-blue-500"
                  )}
                  maxLength={20}
                  disabled={loading}
                />
                <User className="absolute left-4 top-1/2 -translate-y-1/2 h-4 w-4 text-gray-400" />
                {newNickname && (
                  <div className="absolute right-3 top-1/2 -translate-y-1/2">
                    {validationError ? (
                      <X className="h-5 w-5 text-red-500" />
                    ) : isValid ? (
                      <Check className="h-5 w-5 text-green-500" />
                    ) : null}
                  </div>
                )}
              </div>
              
              {/* 验证错误提示 */}
              {validationError && (
                <div className="flex items-center space-x-2 text-red-600 text-sm">
                  <AlertCircle className="h-4 w-4" />
                  <span>{validationError}</span>
                </div>
              )}
              
              {/* 字符计数 */}
              <div className="flex justify-between text-xs text-gray-500">
                <span>支持中英文、数字、常用标点符号</span>
                <span>{newNickname.length}/20</span>
              </div>
            </div>

            {/* 按钮组 */}
            <div className="flex space-x-3 pt-2">
              <Button
                variant="outline"
                onClick={() => handleOpenChange(false)}
                disabled={loading}
                className="flex-1 h-11 rounded-xl border-2"
              >
                取消
              </Button>
              <Button
                onClick={handleSubmit}
                disabled={loading || !isValid || !hasChanges}
                className="flex-1 h-11 bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white font-medium shadow-lg hover:shadow-xl transition-all duration-300 rounded-xl active:scale-[0.98]"
              >
                {loading ? (
                  <>
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    更新中...
                  </>
                ) : (
                  <>
                    <Check className="h-4 w-4 mr-2" />
                    确认更改
                  </>
                )}
              </Button>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}