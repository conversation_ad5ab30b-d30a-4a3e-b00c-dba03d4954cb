# 🤖 SuperClaude 中文关键词Agent自动分配系统使用指南

## 📋 系统概览

这套系统为知识商城项目量身定制，能够根据中文输入自动推荐最合适的Agent，提升开发效率和代码质量。

## 🎯 支持的Agent类型

### 🎨 前端开发类
- **`frontend-developer`** - React组件、UI界面、样式布局
- **关键词**: 组件、界面、React组件、前端、页面、布局、响应式、CSS、Tailwind
- **自动标志**: `--magic --c7`

### 🔧 后端架构类  
- **`backend-architect`** - API接口、数据库、服务器架构
- **关键词**: API、接口、后端、数据库、服务器、路由、认证、权限
- **自动标志**: `--seq --c7`

### 🔒 安全审计类
- **`security-auditor`** - 安全漏洞、认证授权、数据保护
- **关键词**: 安全、漏洞、认证、授权、加密、密钥、防护
- **自动标志**: `--seq --validate --safe-mode`

### 📊 数据库优化类
- **`database-optimizer`** - 查询优化、索引设计、性能调优
- **关键词**: 数据库优化、查询优化、索引、SQL优化、慢查询
- **自动标志**: `--seq --think`

### ⚡ 性能工程师类
- **`performance-engineer`** - 性能优化、加载速度、缓存策略
- **关键词**: 性能优化、性能测试、加载速度、响应时间、缓存
- **自动标志**: `--play --think --validate`

### 🔍 错误侦探类
- **`error-detective`** - Bug调试、故障排除、日志分析
- **关键词**: 错误、Bug、调试、故障排除、问题诊断
- **自动标志**: `--seq --think`

### 🏗️ 系统架构师类
- **`architect`** - 系统设计、架构决策、技术选型
- **关键词**: 架构、系统设计、模块化、可扩展性、设计模式
- **自动标志**: `--seq --ultrathink --c7`

### 🧹 代码重构师类
- **`refactorer`** - 代码重构、质量优化、技术债务管理
- **关键词**: 重构、代码优化、代码清理、技术债务、代码质量
- **自动标志**: `--seq --validate`

### 🧪 测试自动化类
- **`test-automator`** - 自动化测试、测试用例、测试框架
- **关键词**: 测试、单元测试、集成测试、自动化测试
- **自动标志**: `--play --validate`

### 📝 文档专家类
- **`api-documenter`** - API文档、用户手册、开发文档
- **关键词**: 文档、说明、注释、API文档、用户手册
- **自动标志**: `--persona-scribe=zh --c7`

## 🚀 使用方法

### 命令行使用
```bash
# 基本用法
node .claude/agent-matcher.js "你的中文输入"

# 示例
node .claude/agent-matcher.js "优化数据库查询性能"
node .claude/agent-matcher.js "创建用户登录组件"
node .claude/agent-matcher.js "修复安全漏洞"

# 开启调试模式
DEBUG_AGENT_MATCHING=true node .claude/agent-matcher.js "系统架构设计"
```

### 编程接口使用
```javascript
const ChineseKeywordAgentMatcher = require('./.claude/agent-matcher.js');

const matcher = new ChineseKeywordAgentMatcher();
const recommendation = matcher.analyzeAndRecommend("优化数据库性能");

console.log(`推荐Agent: ${recommendation.recommendedAgent}`);
console.log(`置信度: ${recommendation.confidence}%`);
console.log(`推荐命令: ${recommendation.command}`);
```

## 📊 输出格式说明

系统输出包含以下信息：

```javascript
{
  recommendedAgent: "database-optimizer",     // 推荐的Agent名称
  confidence: 85,                            // 匹配置信度 (0-100%)
  priority: 7,                               // Agent优先级 (1-10)
  autoFlags: ["--seq", "--think"],           // 自动激活的标志
  reasons: ["匹配主要关键词: 数据库优化"],    // 匹配原因说明
  analysis: {                                // 输入分析结果
    complexity: 15,                          // 复杂度评分
    hasMultipleSteps: false,                 // 是否多步骤任务
    projectContext: "database",              // 项目上下文
    urgency: false                           // 是否紧急任务
  },
  command: "/Task --seq --think",            // 生成的推荐命令
  alternatives: ["backend-architect", ...]   // 备选Agent
}
```

## 🎯 项目特定优化

系统针对知识商城项目进行了特殊优化：

### 教程管理场景
- **输入**: "创建教程编辑器"、"优化学习进度跟踪"
- **推荐**: `frontend-developer`
- **上下文**: 教程、课程、内容、学习相关

### 密钥系统场景  
- **输入**: "优化密钥验证系统"、"修复解锁逻辑"
- **推荐**: `security-auditor`
- **上下文**: 密钥、解锁、验证、购买相关

### 用户认证场景
- **输入**: "实现邮箱登录"、"优化用户权限"
- **推荐**: `security-auditor`
- **上下文**: 用户、登录、认证、权限相关

### 管理后台场景
- **输入**: "优化管理后台API"、"改进数据统计"
- **推荐**: `backend-architect`
- **上下文**: 管理、后台、控制台相关

## ⚙️ 配置参数

可以通过修改 `.claude/agent-matcher.js` 中的配置调整系统行为：

```javascript
settings: {
  confidence_threshold: 0.75,        // 匹配阈值
  multiple_keywords_boost: 0.15,     // 多关键词加成
  context_relevance_weight: 0.25,    // 上下文权重
  project_specific_weight: 0.20,     // 项目特定权重
  auto_assignment_enabled: true,     // 启用自动分配
  fallback_agent: "general-purpose", // 默认回退Agent
  log_level: "info"                  // 日志级别
}
```

## 🔧 高级功能

### 智能标志组合
系统会根据输入复杂度和任务类型自动组合标志：

- **高复杂度** (>70%): 自动添加 `--ultrathink`
- **中等复杂度** (>40%): 自动添加 `--think`
- **多步骤任务**: 自动添加 `--loop`
- **紧急任务**: 自动添加 `--validate --safe-mode`

### 上下文感知
系统会根据文件路径和项目关键词进行上下文感知：

- **组件文件** (`components/*`): 提升frontend权重
- **API路由** (`app/api/*`): 提升backend权重  
- **认证文件** (`*auth*`): 提升security权重
- **数据库脚本** (`scripts/*.sql`): 提升database权重

## 💡 最佳实践

### 1. 明确描述任务
✅ **好的输入**: "优化PostgreSQL查询性能，解决慢查询问题"
❌ **不好的输入**: "优化一下"

### 2. 包含技术关键词
✅ **好的输入**: "创建React组件实现用户登录界面"
❌ **不好的输入**: "做个登录功能"

### 3. 明确任务复杂度
✅ **好的输入**: "系统性重构整个认证模块，包括前后端和数据库"
❌ **不好的输入**: "改改认证"

### 4. 指明紧急程度
✅ **好的输入**: "紧急修复生产环境的安全漏洞"
❌ **不好的输入**: "修复bug"

## 🎉 使用效果

使用该系统后，你可以：

- ✅ **自动选择最适合的专业Agent**
- ✅ **获得针对性的标志组合建议**
- ✅ **提高任务处理的准确性和效率**
- ✅ **减少手动选择Agent的时间**
- ✅ **获得备选方案和匹配原因说明**

---

📞 **技术支持**: 如有问题或建议，请查看调试输出或修改配置文件进行定制化调整。