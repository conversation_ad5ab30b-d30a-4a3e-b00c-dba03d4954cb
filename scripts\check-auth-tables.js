require('dotenv').config({ path: '.env.local' });
const { createClient } = require('@supabase/supabase-js');

const supabaseAdmin = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY
);

async function checkTablesStructure() {
  console.log('🔍 检查user_sessions和login_logs表结构...');
  
  // 检查user_sessions表
  try {
    const { data: sessions, error: sessionError } = await supabaseAdmin
      .from('user_sessions')
      .select('*')
      .limit(1);
    
    if (sessionError) {
      console.log('❌ user_sessions表错误:', sessionError.message);
    } else {
      console.log('✅ user_sessions表字段:', sessions && sessions[0] ? Object.keys(sessions[0]) : '表为空但存在');
    }
  } catch (e) {
    console.log('❌ user_sessions表检查异常:', e.message);
  }
  
  // 检查login_logs表
  try {
    const { data: logs, error: logError } = await supabaseAdmin
      .from('login_logs')
      .select('*')
      .limit(1);
    
    if (logError) {
      console.log('❌ login_logs表错误:', logError.message);
    } else {
      console.log('✅ login_logs表字段:', logs && logs[0] ? Object.keys(logs[0]) : '表为空但存在');
    }
  } catch (e) {
    console.log('❌ login_logs表检查异常:', e.message);
  }
}

checkTablesStructure();