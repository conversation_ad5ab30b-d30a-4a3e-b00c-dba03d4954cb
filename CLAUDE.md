# CLAUDE.md

开发服务器默认已经开启，地址为：http://localhost:3000/
不需要每次测试都打开新的开发服务器

# 🎯 最新系统更新 (2025年8月4日)

## 🔐 登录系统简化重构
- **移除微信登录**: 取消微信扫码登录，简化用户注册流程
- **邮箱魔法链接登录**: 实现无密码邮箱登录系统，用户体验更佳
- **一键登录**: 点击"登录"按钮直接弹出邮箱验证对话框
- **安全优化**: 魔法链接10分钟有效期，一次性使用，HMAC签名防篡改

### 新增功能
- ✅ 邮箱魔法链接认证系统 (`lib/email-auth.ts`)
- ✅ 魔法链接验证页面 (`app/auth/magic/page.tsx`)
- ✅ 邮箱登录对话框组件 (`components/email-login-dialog.tsx`)
- ✅ 用户认证数据表结构 (`scripts/03-auth-tables.sql`)
- ✅ 登录API接口 (`app/api/auth/magic-link/route.ts`, `app/api/auth/magic/route.ts`)

### 保留的备用系统
- 📦 微信登录相关文件已保留但未激活，可随时恢复
- 📦 手机验证码登录系统 (`lib/phone-auth.ts`) 已实现但未启用

# 🎯 完整的 Kiro IDE 风格指导文档

本项目已完成完整的 Kiro IDE 风格文档体系建设，包含 6 个核心文档，为 Claude Code 提供全面的项目上下文和开发指导。

## 📁 文档结构概览

```
知识商城/
├── .claude/steering/          # 核心指导文档
│   ├── product.md            # ✅ 产品战略指导
│   ├── tech.md               # ✅ 技术架构指导
│   └── structure.md          # ✅ 项目结构指导
├── specs/                    # 详细规范文档
│   ├── design.md             # ✅ 设计规范
│   ├── requirements.md       # ✅ 需求规范
│   └── tasks.md              # ✅ 任务规范
└── CLAUDE.md                 # ✅ 项目配置中心
```

## 🔍 文档内容价值

### 指导文档核心价值
- **产品指导**: 明确产品愿景、目标用户、核心功能和商业模式
- **技术指导**: 定义技术栈、架构约束、安全要求和性能标准
- **结构指导**: 规范项目组织、命名约定、代码组织和扩展策略

### 规范文档详细内容
- **设计规范**: 完整的设计系统、组件规范、交互标准和响应式设计
- **需求规范**: 详细的功能需求、非功能需求、技术约束和业务规则
- **任务规范**: 任务管理框架、优先级矩阵、质量标准和自动化工具

## 💡 文档特色功能

### 学习进度跟踪系统 (核心创新)
- 智能内容解析和章节导航
- 实时进度计算和数据同步
- 多设备学习状态管理
- 个性化学习体验

### 双编辑器系统 (创新设计)
- 普通编辑器: TipTap 富文本编辑
- 结构化编辑器: 支持学习跟踪的增强编辑器
- 媒体管理和版本控制
- 实时预览和自动保存

### 技术架构亮点
- Next.js 14 + App Router 架构
- Supabase + PostgreSQL 云端数据库
- TypeScript 5+ 严格类型系统
- shadcn/ui + Tailwind CSS 设计系统

## 🚀 使用效果

### Claude Code 体验提升
- **上下文感知**: 完整的项目背景和技术约束
- **代码一致性**: 遵循项目模式和最佳实践
- **质量保证**: 自动应用质量标准和安全要求
- **开发效率**: 减少重复说明，聚焦核心开发

### 开发团队受益
- **新成员入门**: 快速理解项目全貌和技术栈
- **协作一致性**: 统一的开发规范和代码风格
- **质量把控**: 明确的验收标准和测试要求
- **持续改进**: 结构化的任务管理和优化策略

这套完整的文档体系确保了知识商城项目的高质量开发和可持续维护。

---

This file provides guidance to Claude Code (claude.ai/code) when working with code in this repository.

## 项目概述

这是一个基于 Next.js 14 的知识商城系统，实现教程内容的密钥验证和销售功能。用户通过购买密钥来解锁教程内容，管理员可以通过后台管理教程和密钥。

## 开发命令

- `pnpm dev` - 启动开发服务器
- `pnpm build` - 构建生产版本
- `pnpm start` - 启动生产服务器
- `pnpm lint` - 运行 ESLint 检查

注意：项目配置中禁用了构建时的 TypeScript 和 ESLint 错误检查（在 next.config.mjs 中）

## 核心架构

### 数据库设计
系统使用 PostgreSQL 数据库，包含以下核心表：
- `categories` - 教程分类
- `tutorials` - 教程内容（标题、描述、内容、价格等）
- `tutorial_keys` - 教程密钥（24位大写字母数字组合）
- `user_unlocks` - 用户解锁记录
- `system_config` - 系统配置

### API 路由结构
```
/api
├── admin/          # 管理员功能
│   ├── auth/       # 管理员认证
│   ├── categories/ # 分类管理
│   ├── keys/       # 密钥管理
│   ├── stats/      # 统计数据
│   └── tutorials/  # 教程管理
├── public/         # 公开接口
│   ├── categories/ # 公开分类列表
│   └── tutorials/  # 公开教程列表
├── tutorial/[id]/  # 单个教程获取
├── user-unlocks/   # 用户解锁记录
└── verify-key/     # 密钥验证
```

### 页面结构
- `/` - 首页（教程展示和密钥验证）
- `/tutorial/[id]` - 教程详情页
- `/my-tutorials` - 用户已解锁教程
- `/admin` - 管理后台首页
- `/admin/create-tutorial` - 创建教程

### 核心功能模块

1. **密钥生成系统** (`lib/key-generator.ts`)
   - 生成24位唯一密钥
   - 支持批量生成
   - 密钥格式验证

2. **认证系统** (`lib/auth.ts`)
   - 管理员认证（目前为演示版本，直接返回 true）
   - 用户身份识别（基于 IP + User-Agent）
   - 频率限制检查

3. **数据库操作** (`lib/database.ts`)
   - PostgreSQL 连接池
   - 查询和事务辅助函数

### UI 组件系统
使用 shadcn/ui 组件库，基于 Radix UI + Tailwind CSS：
- 组件位于 `components/ui/` 目录
- 使用 `components.json` 配置
- 支持主题切换（dark/light mode）

### 特殊文件说明
- 数据库初始化脚本：`scripts/01-create-tables.sql` 和 `scripts/02-seed-data.sql`
- 微信登录组件：`components/wechat-login-dialog.tsx`
- 路径别名配置：`@/*` 指向项目根目录

## 开发约定

- 使用 TypeScript 进行类型安全开发
- 界面文本使用中文
- 密钥格式固定为24位大写字母和数字
- 使用 PostgreSQL 数组类型存储标签
- API 响应统一使用 JSON 格式
- 错误处理包含用户友好的中文提示

### 最新系统特性 (2025年8月3日更新)

#### 管理员权限优化
- **移除右上角管理按钮**: 管理功能隐藏到左上角知识商城标题中
- **智能访问控制**: 点击标题即可进入管理后台，保持界面简洁
- **微信登录显示**: 登录后显示用户名(优先级：nickname > username > '用户')

#### 公告系统扩展 (5种类型支持)
- **标准类型**: info、warning、success、error
- **新增类型**: update (系统功能更新通知)
- **智能清除**: 全部已读、清除全部消息功能
- **类型样式**: 每种类型都有独特的颜色和图标标识

#### 后台管理优化
- **认证系统优化**: JWT token有效期延长至8小时，减少重新验证频率
- **智能数据刷新**: `refreshDataSmart()` 函数，避免频繁API调用
- **认证中间件**: 所有API请求自动添加认证头，提升安全性
- **延迟跳转**: 认证失败时友好提示，避免突然跳转影响用户体验

## 环境变量要求
- `DATABASE_URL` - PostgreSQL 数据库连接字符串
- `NODE_ENV` - 环境模式（development/production）

## 📋 项目指导文档

为了提供最佳的开发体验和代码生成质量，本项目配置了完整的 Kiro IDE 风格指导文档：

### 指导文档 (.claude/steering/)
- **产品指导** (`.claude/steering/product.md`) - 产品愿景、用户需求、业务目标
- **技术指导** (`.claude/steering/tech.md`) - 技术栈、架构约束、编码规范  
- **结构指导** (`.claude/steering/structure.md`) - 项目组织、命名约定、文件结构

### 规范文档 (specs/)
- **设计规范** (`specs/design.md`) - UI/UX设计系统、组件规范、交互标准
- **需求规范** (`specs/requirements.md`) - 功能需求、非功能需求、业务规则
- **任务规范** (`specs/tasks.md`) - 开发任务管理、优先级矩阵、质量标准

### 使用说明
这些文档为 Claude Code 提供了完整的项目上下文，包括：
- ✅ 产品定位和目标用户群体
- ✅ 完整的技术栈和架构约束 
- ✅ 代码规范和最佳实践
- ✅ UI/UX 设计标准和组件规范
- ✅ 详细的功能需求和技术需求
- ✅ 任务管理框架和质量标准

每次开发时，Claude Code 会自动参考这些指导文档，确保生成的代码符合项目标准和最佳实践。