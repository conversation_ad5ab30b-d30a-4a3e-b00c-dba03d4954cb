// 执行魔法链接认证表结构创建脚本
const { createClient } = require('@supabase/supabase-js');
require('dotenv').config({ path: '.env.local' });

// 创建管理员客户端
const supabaseAdmin = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY,
  {
    auth: {
      autoRefreshToken: false,
      persistSession: false
    }
  }
);

// 完整的认证表结构SQL
const authTablesSQL = `
-- 邮箱魔法链接认证系统数据表
-- 添加到现有数据库中

-- 1. 用户表 (扩展现有表结构)
CREATE TABLE IF NOT EXISTS users (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  email VARCHAR(255) UNIQUE,
  username VARCHA<PERSON>(100),
  nickname VARCHAR(100),
  avatar_url TEXT,
  auth_type VARCHAR(50) DEFAULT 'email', -- email, wechat, phone, anonymous
  email_verified BOOLEAN DEFAULT FALSE,
  phone VARCHAR(20),
  phone_verified BOOLEAN DEFAULT FALSE,
  status VARCHAR(20) DEFAULT 'active', -- active, suspended, deleted
  login_count INTEGER DEFAULT 0,
  last_login_at TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 2. 魔法链接表
CREATE TABLE IF NOT EXISTS magic_links (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  email VARCHAR(255) NOT NULL,
  token VARCHAR(128) NOT NULL UNIQUE,
  signature VARCHAR(128) NOT NULL,
  expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
  used BOOLEAN DEFAULT FALSE,
  used_at TIMESTAMP WITH TIME ZONE,
  ip_address INET,
  user_agent TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 3. 手机验证码表
CREATE TABLE IF NOT EXISTS phone_codes (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  phone VARCHAR(20) NOT NULL,
  code VARCHAR(10) NOT NULL,
  type VARCHAR(20) DEFAULT 'login', -- login, register, reset
  expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
  used BOOLEAN DEFAULT FALSE,
  used_at TIMESTAMP WITH TIME ZONE,
  attempts INTEGER DEFAULT 0,
  ip_address INET,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 4. 用户会话表
CREATE TABLE IF NOT EXISTS user_sessions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES users(id) ON DELETE CASCADE,
  session_token VARCHAR(128) NOT NULL UNIQUE,
  expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
  ip_address INET,
  user_agent TEXT,
  last_activity TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 5. 登录日志表
CREATE TABLE IF NOT EXISTS login_logs (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES users(id) ON DELETE SET NULL,
  email VARCHAR(255),
  auth_type VARCHAR(50), -- email, wechat, phone, anonymous
  status VARCHAR(20), -- success, failed, blocked
  ip_address INET,
  user_agent TEXT,
  error_message TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 6. 社交账号绑定表
CREATE TABLE IF NOT EXISTS social_accounts (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES users(id) ON DELETE CASCADE,
  provider VARCHAR(50) NOT NULL, -- wechat, qq, alipay
  provider_id VARCHAR(100) NOT NULL, -- openid, unionid
  provider_data JSONB, -- 存储额外的提供商数据
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(provider, provider_id)
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);
CREATE INDEX IF NOT EXISTS idx_users_phone ON users(phone);
CREATE INDEX IF NOT EXISTS idx_users_auth_type ON users(auth_type);

CREATE INDEX IF NOT EXISTS idx_magic_links_email ON magic_links(email);
CREATE INDEX IF NOT EXISTS idx_magic_links_token ON magic_links(token);
CREATE INDEX IF NOT EXISTS idx_magic_links_expires_at ON magic_links(expires_at);

CREATE INDEX IF NOT EXISTS idx_phone_codes_phone ON phone_codes(phone);
CREATE INDEX IF NOT EXISTS idx_phone_codes_expires_at ON phone_codes(expires_at);

CREATE INDEX IF NOT EXISTS idx_user_sessions_user_id ON user_sessions(user_id);
CREATE INDEX IF NOT EXISTS idx_user_sessions_token ON user_sessions(session_token);
CREATE INDEX IF NOT EXISTS idx_user_sessions_expires_at ON user_sessions(expires_at);

CREATE INDEX IF NOT EXISTS idx_login_logs_user_id ON login_logs(user_id);
CREATE INDEX IF NOT EXISTS idx_login_logs_created_at ON login_logs(created_at);

CREATE INDEX IF NOT EXISTS idx_social_accounts_user_id ON social_accounts(user_id);
CREATE INDEX IF NOT EXISTS idx_social_accounts_provider ON social_accounts(provider, provider_id);

-- 更新时间触发器
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_social_accounts_updated_at BEFORE UPDATE ON social_accounts
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- 清理过期数据的函数
CREATE OR REPLACE FUNCTION cleanup_expired_auth_data()
RETURNS INTEGER AS $$
DECLARE
    deleted_count INTEGER := 0;
BEGIN
    -- 清理过期的魔法链接
    DELETE FROM magic_links WHERE expires_at < NOW();
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    
    -- 清理过期的验证码
    DELETE FROM phone_codes WHERE expires_at < NOW();
    
    -- 清理过期的会话
    DELETE FROM user_sessions WHERE expires_at < NOW();
    
    -- 清理30天前的登录日志
    DELETE FROM login_logs WHERE created_at < NOW() - INTERVAL '30 days';
    
    RETURN deleted_count;
END;
$$ LANGUAGE plpgsql;
`;

async function createAuthTables() {
  try {
    console.log('🏗️ 开始创建认证表结构...');
    
    // 执行SQL脚本
    const { data, error } = await supabaseAdmin
      .rpc('exec', { sql: authTablesSQL });
    
    if (error) {
      console.error('❌ SQL执行失败:', error);
      
      // 尝试逐个创建表
      console.log('🔄 尝试逐个创建表...');
      const statements = authTablesSQL
        .split(';')
        .map(stmt => stmt.trim())
        .filter(stmt => stmt.length > 0);
      
      for (let i = 0; i < statements.length; i++) {
        const statement = statements[i];
        if (statement.startsWith('--') || statement.length < 10) continue;
        
        try {
          console.log(`执行语句 ${i + 1}/${statements.length}...`);
          const { error: stmtError } = await supabaseAdmin
            .rpc('exec', { sql: statement });
          
          if (stmtError) {
            console.log(`⚠️ 语句执行失败:`, stmtError.message);
          } else {
            console.log(`✅ 语句执行成功`);
          }
        } catch (err) {
          console.log(`❌ 语句异常:`, err.message);
        }
      }
    } else {
      console.log('✅ 认证表结构创建成功');
    }
    
    // 验证表创建结果
    console.log('\n🔍 验证表创建结果...');
    const tables = [
      'users',
      'magic_links', 
      'phone_codes',
      'user_sessions',
      'login_logs',
      'social_accounts'
    ];
    
    let successCount = 0;
    for (const tableName of tables) {
      try {
        const { data, error } = await supabaseAdmin
          .from(tableName)
          .select('*')
          .limit(1);
        
        if (error) {
          console.log(`❌ 表 ${tableName} 创建失败:`, error.message);
        } else {
          console.log(`✅ 表 ${tableName} 创建成功`);
          successCount++;
        }
      } catch (err) {
        console.log(`❌ 表 ${tableName} 检查异常:`, err.message);
      }
    }
    
    console.log(`\\n🎯 表创建汇总: ${successCount}/${tables.length} 个表创建成功`);
    
    if (successCount === tables.length) {
      console.log('🎉 所有认证表创建完成，系统已就绪！');
    } else {
      console.log('⚠️ 部分表创建失败，请检查错误信息');
    }
    
    return successCount === tables.length;
    
  } catch (error) {
    console.error('❌ 创建表结构异常:', error);
    return false;
  }
}

// 主函数：检测并创建表结构
async function setupDatabase() {
  console.log('🚀 Supabase认证系统数据库初始化');
  console.log('=' .repeat(50));
  
  try {
    // 测试连接
    console.log('🔍 测试数据库连接...');
    const { data, error } = await supabaseAdmin
      .from('information_schema.tables')
      .select('table_name')
      .eq('table_schema', 'public')
      .limit(1);
    
    if (error) {
      console.error('❌ 数据库连接失败:', error.message);
      return false;
    }
    
    console.log('✅ 数据库连接正常');
    
    // 创建表结构
    const success = await createAuthTables();
    
    if (success) {
      console.log('\\n🎯 数据库初始化完成！');
      console.log('💡 现在可以测试魔法链接登录功能了');
    } else {
      console.log('\\n⚠️ 数据库初始化未完全成功，请检查错误信息');
    }
    
    return success;
    
  } catch (error) {
    console.error('❌ 数据库初始化异常:', error);
    return false;
  }
}

// 运行初始化
setupDatabase()
  .then(success => {
    process.exit(success ? 0 : 1);
  })
  .catch(error => {
    console.error('初始化过程出错:', error);
    process.exit(1);
  });