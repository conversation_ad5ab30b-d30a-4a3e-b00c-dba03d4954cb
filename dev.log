
> my-v0-project@0.1.0 dev F:\编程项目\知识商城
> next dev

 ⚠ Port 3000 is in use, trying 3001 instead.
  ▲ Next.js 14.2.30
  - Local:        http://localhost:3001
  - Environments: .env.local

 ✓ Starting...
 ✓ Ready in 9.8s
 ○ Compiling /api/auth/email/send-code ...
 ✓ Compiled /api/auth/email/send-code in 1548ms (154 modules)
📧 收到验证码发送请求: { email: '<EMAIL>', type: 'register' }
✅ 邮箱可用，允许发送注册验证码
📧 生成邮箱验证码: {
  email: '<EMAIL>',
  code: '159887',
  type: 'register'
}
✅ 验证码生成成功: { email: '<EMAIL>', type: 'register' }
📧 准备发送邮箱验证码: {
  email: '<EMAIL>',
  code: '159887',
  type: 'register'
}

╭─────────────────────────────────────╮
│  📧 知识商城 - 邮箱验证码            │
├─────────────────────────────────────┤
│  收件人: <EMAIL>       │
│  验证码: 159887                      │
│  用途: 注册验证                       │
│  有效期: 10分钟                      │
╰─────────────────────────────────────╯
    
✅ 验证码发送成功: {
  email: '<EMAIL>',
  type: 'register',
  expiresAt: '2025-08-06T08:16:13.399Z'
}
 POST /api/auth/email/send-code 200 in 8656ms
 ✓ Compiled /api/auth/email/register in 170ms (156 modules)
🔍 邮箱注册请求: { email: '<EMAIL>' }
✅ 邮箱可用，开始验证验证码
🔍 开始验证邮箱验证码: {
  email: '<EMAIL>',
  code: '159887',
  type: 'register'
}
✅ 邮箱验证码验证成功: {
  email: '<EMAIL>',
  userId: 'pending',
  isNewUser: true,
  type: 'register'
}
✅ 验证码验证成功，开始创建用户
👤 创建用户: { email: '<EMAIL>' }
✅ 用户创建成功: {
  email: '<EMAIL>',
  userId: 'ed7a0bb4-858f-4604-a4fb-02c15ccd1817'
}
🔐 创建注册会话令牌: { sessionToken: '7eff988a...' }
❌ 注册会话创建失败: {
  code: '22P02',
  details: null,
  hint: null,
  message: 'invalid input syntax for type inet: "unknown"'
}
 POST /api/auth/email/register 500 in 2157ms
 ✓ Compiled in 206ms (156 modules)
 ✓ Compiled in 178ms (156 modules)
 ✓ Compiled in 34ms
 ✓ Compiled /api/auth/email/send-code in 203ms (154 modules)
📧 收到验证码发送请求: { email: '<EMAIL>', type: 'register' }
✅ 邮箱可用，允许发送注册验证码
📧 生成邮箱验证码: {
  email: '<EMAIL>',
  code: '801044',
  type: 'register'
}
✅ 验证码生成成功: { email: '<EMAIL>', type: 'register' }
📧 准备发送邮箱验证码: {
  email: '<EMAIL>',
  code: '801044',
  type: 'register'
}

╭─────────────────────────────────────╮
│  📧 知识商城 - 邮箱验证码            │
├─────────────────────────────────────┤
│  收件人: <EMAIL>       │
│  验证码: 801044                      │
│  用途: 注册验证                       │
│  有效期: 10分钟                      │
╰─────────────────────────────────────╯
    
✅ 验证码发送成功: {
  email: '<EMAIL>',
  type: 'register',
  expiresAt: '2025-08-06T08:18:20.902Z'
}
 POST /api/auth/email/send-code 200 in 7555ms
 ✓ Compiled /api/auth/email/register in 123ms (156 modules)
🔍 邮箱注册请求: { email: '<EMAIL>' }
✅ 邮箱可用，开始验证验证码
🔍 开始验证邮箱验证码: {
  email: '<EMAIL>',
  code: '801044',
  type: 'register'
}
✅ 邮箱验证码验证成功: {
  email: '<EMAIL>',
  userId: 'pending',
  isNewUser: true,
  type: 'register'
}
✅ 验证码验证成功，开始创建用户
👤 创建用户: { email: '<EMAIL>' }
✅ 用户创建成功: {
  email: '<EMAIL>',
  userId: '3f9ac109-db8d-48d5-b6f6-17b6d92211fb'
}
🔐 创建注册会话令牌: { sessionToken: '50d272d6...' }
✅ 邮箱注册成功: {
  email: '<EMAIL>',
  userId: '3f9ac109-db8d-48d5-b6f6-17b6d92211fb',
  sessionToken: '50d272d6...'
}
 POST /api/auth/email/register 200 in 1914ms
📧 收到验证码发送请求: { email: '<EMAIL>', type: 'register' }
✅ 邮箱可用，允许发送注册验证码
📧 生成邮箱验证码: {
  email: '<EMAIL>',
  code: '388654',
  type: 'register'
}
✅ 验证码生成成功: { email: '<EMAIL>', type: 'register' }
📧 准备发送邮箱验证码: {
  email: '<EMAIL>',
  code: '388654',
  type: 'register'
}

╭─────────────────────────────────────╮
│  📧 知识商城 - 邮箱验证码            │
├─────────────────────────────────────┤
│  收件人: <EMAIL>       │
│  验证码: 388654                      │
│  用途: 注册验证                       │
│  有效期: 10分钟                      │
╰─────────────────────────────────────╯
    
✅ 验证码发送成功: {
  email: '<EMAIL>',
  type: 'register',
  expiresAt: '2025-08-06T08:19:23.095Z'
}
 POST /api/auth/email/send-code 200 in 6758ms
🔍 邮箱注册请求: { email: '<EMAIL>' }
✅ 邮箱可用，开始验证验证码
🔍 开始验证邮箱验证码: {
  email: '<EMAIL>',
  code: '388654',
  type: 'register'
}
✅ 邮箱验证码验证成功: {
  email: '<EMAIL>',
  userId: 'pending',
  isNewUser: true,
  type: 'register'
}
✅ 验证码验证成功，开始创建用户
👤 创建用户: { email: '<EMAIL>' }
✅ 用户创建成功: {
  email: '<EMAIL>',
  userId: 'f0a8a67c-0927-42ce-8741-f63a933faf45'
}
🔐 创建注册会话令牌: { sessionToken: '6fa0357b...' }
✅ 邮箱注册成功: {
  email: '<EMAIL>',
  userId: 'f0a8a67c-0927-42ce-8741-f63a933faf45',
  sessionToken: '6fa0357b...'
}
 POST /api/auth/email/register 200 in 1673ms
📧 收到验证码发送请求: { email: '<EMAIL>', type: 'register' }
⚠️ 注册验证码请求失败 - 邮箱已存在: {
  email: '<EMAIL>',
  userId: 'f0a8a67c-0927-42ce-8741-f63a933faf45',
  nickname: '用户316',
  createdAt: '2025-08-06T08:09:26.115086+00:00'
}
 POST /api/auth/email/send-code 400 in 528ms
🔍 邮箱注册请求: { email: '<EMAIL>' }
✅ 邮箱可用，开始验证验证码
🔍 开始验证邮箱验证码: {
  email: '<EMAIL>',
  code: '999999',
  type: 'register'
}
❌ 验证码不存在或已使用
❌ 验证码验证失败: 验证码错误或已过期
 POST /api/auth/email/register 400 in 789ms
