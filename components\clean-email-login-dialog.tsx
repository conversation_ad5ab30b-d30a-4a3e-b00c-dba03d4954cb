"use client"

import { useState, useEffect, useRef } from "react"
import { <PERSON><PERSON>, DialogContent } from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { 
  Mail, 
  Eye, 
  EyeOff, 
  RefreshCw,
  User,
  UserPlus,
  Key,
  Check,
  X,
  Shield,
  Sparkles,
  ArrowRight,
  Loader2,
  Lock
} from "lucide-react"
import { useToast } from "@/hooks/use-toast"
import { cn } from "@/lib/utils"

interface CleanEmailLoginDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  onLoginSuccess: (userInfo: any) => void
}

export function CleanEmailLoginDialog({ open, onOpenChange, onLoginSuccess }: CleanEmailLoginDialogProps) {
  // 表单状态
  const [email, setEmail] = useState("")
  const [password, setPassword] = useState("")
  const [confirmPassword, setConfirmPassword] = useState("")
  const [emailCode, setEmailCode] = useState("")
  const [captchaCode, setCaptchaCode] = useState("")
  const [showPassword, setShowPassword] = useState(false)
  const [showConfirmPassword, setShowConfirmPassword] = useState(false)
  
  // 验证码状态
  const [emailCodeSent, setEmailCodeSent] = useState(false)
  const [emailCountdown, setEmailCountdown] = useState(0)
  const [captchaSession, setCaptchaSession] = useState("")
  
  // 加载状态
  const [loading, setLoading] = useState(false)
  const [sendingEmailCode, setSendingEmailCode] = useState(false)
  
  // 模式状态: 'login' | 'register' | 'forgot'
  const [mode, setMode] = useState<'login' | 'register' | 'forgot'>('login')
  
  // 实时验证状态
  const [emailValid, setEmailValid] = useState<boolean | null>(null)
  const [passwordStrength, setPasswordStrength] = useState<'weak' | 'medium' | 'strong' | null>(null)
  const [captchaValid, setCaptchaValid] = useState<boolean | null>(null)
  
  // 图形验证码
  const captchaRef = useRef<HTMLImageElement>(null)
  
  const { toast } = useToast()

  // 验证邮箱格式
  const validateEmail = (email: string) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    return emailRegex.test(email)
  }

  // 验证密码强度
  const validatePassword = (password: string) => {
    if (password.length < 6) {
      return { valid: false, error: '密码长度至少6位' }
    }
    if (password.length < 8) {
      return { valid: true, strength: 'weak' as const }
    }
    if (!/(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/.test(password)) {
      return { valid: true, strength: 'medium' as const }
    }
    return { valid: true, strength: 'strong' as const }
  }

  // 实时邮箱验证
  useEffect(() => {
    if (email) {
      setEmailValid(validateEmail(email))
    } else {
      setEmailValid(null)
    }
  }, [email])

  // 实时密码强度检查
  useEffect(() => {
    if (password) {
      const result = validatePassword(password)
      setPasswordStrength(result.strength || null)
    } else {
      setPasswordStrength(null)
    }
  }, [password])

  // 加载图形验证码
  const loadCaptcha = async () => {
    try {
      const response = await fetch('/api/captcha/generate', {
        method: 'GET',
        cache: 'no-cache'
      })
      
      if (response.ok) {
        const sessionId = response.headers.get('X-Captcha-Session')
        if (sessionId) {
          setCaptchaSession(sessionId)
        }
        
        const svgBlob = await response.blob()
        const url = URL.createObjectURL(svgBlob)
        
        if (captchaRef.current) {
          captchaRef.current.src = url
        }
        setCaptchaValid(null) // 重置验证状态
      }
    } catch (error) {
      console.error('加载验证码失败:', error)
    }
  }

  // 验证图形验证码
  const verifyCaptchaInput = async (code: string) => {
    if (!code || !captchaSession) {
      setCaptchaValid(null)
      return
    }
    
    if (code.length !== 4) {
      setCaptchaValid(null)
      return
    }

    try {
      const response = await fetch('/api/captcha/verify', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          sessionId: captchaSession,
          code: code
        })
      })
      
      const data = await response.json()
      setCaptchaValid(data.success)
      
      if (!data.success) {
        setTimeout(() => {
          loadCaptcha()
          setCaptchaCode("")
        }, 1000)
      }
    } catch (error) {
      setCaptchaValid(false)
      console.error('验证图形验证码失败:', error)
    }
  }

  // 实时验证图形验证码
  useEffect(() => {
    if (captchaCode) {
      verifyCaptchaInput(captchaCode)
    } else {
      setCaptchaValid(null)
    }
  }, [captchaCode, captchaSession])

  // 发送邮箱验证码（仅用于注册和找回密码）
  const sendEmailCode = async () => {
    if (!email || !validateEmail(email)) {
      toast({
        title: "请输入正确的邮箱地址",
        variant: "destructive",
      })
      return
    }

    // 只有注册和找回密码模式需要先验证图形验证码（但只在提交时验证，获取验证码时不需要）
    // 注释掉发送邮箱验证码时的图形验证码检查
    // if ((mode === 'register' || mode === 'forgot') && !captchaValid) {
    //   toast({
    //     title: "请先完成图形验证码验证",
    //     variant: "destructive",
    //   })
    //   return
    // }

    setSendingEmailCode(true)
    
    try {
      const type = mode === 'register' ? 'register' : 'reset_password'
      
      const response = await fetch('/api/auth/email/send-code', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email, type })
      })
      
      const data = await response.json()
      
      if (data.success) {
        setEmailCodeSent(true)
        setEmailCountdown(60)
        
        // 启动倒计时
        const timer = setInterval(() => {
          setEmailCountdown(prev => {
            if (prev <= 1) {
              clearInterval(timer)
              return 0
            }
            return prev - 1
          })
        }, 1000)
        
        toast({
          title: "验证码发送成功",
          description: "请查收您的邮箱",
        })
        
        // 重新加载图形验证码
        loadCaptcha()
      } else {
        toast({
          title: "发送失败",
          description: data.error || "请稍后重试",
          variant: "destructive",
        })
      }
    } catch (error) {
      console.error('发送邮箱验证码失败:', error)
      toast({
        title: "网络错误",
        description: "请检查网络连接后重试",
        variant: "destructive",
      })
    } finally {
      setSendingEmailCode(false)
    }
  }

  // 密码登录
  const handleLogin = async () => {
    if (!email || !validateEmail(email)) {
      toast({
        title: "请输入正确的邮箱地址",
        variant: "destructive",
      })
      return
    }

    if (!password) {
      toast({
        title: "请输入密码",
        variant: "destructive",
      })
      return
    }

    // 登录时需要验证图形验证码
    if (!captchaValid) {
      toast({
        title: "请先完成图形验证码验证",
        variant: "destructive",
      })
      return
    }

    setLoading(true)
    
    try {
      const response = await fetch('/api/auth/email/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ 
          email, 
          password,
          loginType: 'password' 
        })
      })
      
      const data = await response.json()
      
      if (data.success) {
        onLoginSuccess(data.user)
        onOpenChange(false)
        toast({
          title: "登录成功",
          description: `欢迎回来 ${data.user.nickname || data.user.username || data.user.email}！`,
        })
      } else {
        toast({
          title: "登录失败",
          description: data.error || "邮箱或密码错误",
          variant: "destructive",
        })
        // 登录失败时重新加载验证码
        loadCaptcha()
        setCaptchaCode("")
      }
    } catch (error) {
      console.error('登录失败:', error)
      toast({
        title: "登录异常",
        description: "请重新尝试",
        variant: "destructive",
      })
    } finally {
      setLoading(false)
    }
  }

  // 注册（需要邮箱验证码）
  const handleRegister = async () => {
    if (!email || !validateEmail(email)) {
      toast({
        title: "请输入正确的邮箱地址",
        variant: "destructive",
      })
      return
    }

    if (!emailCode) {
      toast({
        title: "请输入邮箱验证码",
        variant: "destructive",
      })
      return
    }

    if (!password) {
      toast({
        title: "请设置密码",
        variant: "destructive",
      })
      return
    }

    const passwordValidation = validatePassword(password)
    if (!passwordValidation.valid) {
      toast({
        title: "密码格式错误",
        description: passwordValidation.error,
        variant: "destructive",
      })
      return
    }

    if (password !== confirmPassword) {
      toast({
        title: "密码不一致",
        description: "请确认两次输入的密码相同",
        variant: "destructive",
      })
      return
    }

    // 注册时需要验证图形验证码
    if (!captchaValid) {
      toast({
        title: "请先完成图形验证码验证",
        variant: "destructive",
      })
      return
    }

    setLoading(true)
    
    try {
      const response = await fetch('/api/auth/email/register', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ 
          email, 
          code: emailCode,
          password 
        })
      })
      
      const data = await response.json()
      
      if (data.success) {
        onLoginSuccess(data.user)
        onOpenChange(false)
        toast({
          title: "注册成功",
          description: "账户创建完成，欢迎使用知识商城！",
        })
      } else {
        toast({
          title: "注册失败",
          description: data.error || "验证码错误或邮箱已存在",
          variant: "destructive",
        })
      }
    } catch (error) {
      console.error('注册失败:', error)
      toast({
        title: "注册异常",
        description: "请重新尝试",
        variant: "destructive",
      })
    } finally {
      setLoading(false)
    }
  }

  // 忘记密码处理
  const handleForgotPassword = async () => {
    if (!email || !validateEmail(email)) {
      toast({
        title: "请输入正确的邮箱地址",
        variant: "destructive",
      })
      return
    }

    if (!emailCode) {
      toast({
        title: "请输入邮箱验证码",
        variant: "destructive",
      })
      return
    }

    if (!password) {
      toast({
        title: "请输入新密码",
        variant: "destructive",
      })
      return
    }

    const passwordValidation = validatePassword(password)
    if (!passwordValidation.valid) {
      toast({
        title: "密码格式错误",
        description: passwordValidation.error,
        variant: "destructive",
      })
      return
    }

    if (password !== confirmPassword) {
      toast({
        title: "密码不一致",
        description: "请确认两次输入的密码相同",
        variant: "destructive",
      })
      return
    }

    setLoading(true)
    
    try {
      const response = await fetch('/api/auth/email/reset-password', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ 
          email, 
          code: emailCode,
          newPassword: password 
        })
      })
      
      const data = await response.json()
      
      if (data.success) {
        toast({
          title: "密码重置成功",
          description: "请使用新密码登录",
        })
        setMode('login')
        setPassword('')
        setConfirmPassword('')
        setEmailCode('')
      } else {
        toast({
          title: "密码重置失败",
          description: data.error || "验证码无效或用户不存在",
          variant: "destructive",
        })
      }
    } catch (error) {
      console.error('密码重置失败:', error)
      toast({
        title: "重置异常",
        description: "请重新尝试",
        variant: "destructive",
      })
    } finally {
      setLoading(false)
    }
  }

  // 重置表单
  const resetForm = () => {
    setEmail("")
    setPassword("")
    setConfirmPassword("")
    setEmailCode("")
    setCaptchaCode("")
    setEmailCodeSent(false)
    setEmailCountdown(0)
    setShowPassword(false)
    setShowConfirmPassword(false)
    setLoading(false)
    setSendingEmailCode(false)
    setMode('login')
    setCaptchaSession('')
    setEmailValid(null)
    setPasswordStrength(null)
    setCaptchaValid(null)
  }

  // 监听对话框打开
  useEffect(() => {
    if (open) {
      loadCaptcha()
    } else {
      resetForm()
    }
  }, [open])

  // 监听对话框关闭
  const handleOpenChange = (isOpen: boolean) => {
    if (!isOpen) {
      resetForm()
    }
    onOpenChange(isOpen)
  }

  // 获取标题和描述
  const getTitleAndDescription = () => {
    switch (mode) {
      case 'login':
        return {
          title: '密码登录',
          description: '使用邮箱和密码登录您的账户'
        }
      case 'register':
        return {
          title: '注册账户',
          description: '创建新账户享受优质教程内容'
        }
      case 'forgot':
        return {
          title: '重置密码',
          description: '通过邮箱验证重置您的密码'
        }
    }
  }

  const { title, description } = getTitleAndDescription()

  return (
    <Dialog open={open} onOpenChange={handleOpenChange}>
      <DialogContent className="sm:max-w-[440px] p-0 gap-0 overflow-hidden bg-white border-0 shadow-2xl">
        {/* 现代化背景装饰 */}
        <div className="absolute inset-0 bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50">
          <div className="absolute top-0 left-0 w-32 h-32 bg-gradient-to-br from-blue-400/20 to-purple-400/20 rounded-full blur-xl"></div>
          <div className="absolute bottom-0 right-0 w-40 h-40 bg-gradient-to-tl from-indigo-400/20 to-pink-400/20 rounded-full blur-xl"></div>
        </div>
        
        {/* 内容区域 */}
        <div className="relative backdrop-blur-sm bg-white/80 p-8 space-y-6">
          {/* 标题区域 */}
          <div className="text-center space-y-2">
            <div className="flex items-center justify-center mb-2">
              <div className="p-3 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full shadow-lg animate-pulse">
                <Sparkles className="h-6 w-6 text-white" />
              </div>
            </div>
            <h2 className="text-2xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
              {title}
            </h2>
            <p className="text-gray-600 text-sm">{description}</p>
          </div>

          <div className="space-y-5">
            {/* 邮箱地址 */}
            <div className="space-y-2">
              <div className="relative">
                <Input
                  type="email"
                  placeholder="邮箱地址"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  className={cn(
                    "h-12 pl-12 pr-10 bg-white/60 backdrop-blur-sm border-2 transition-all duration-300 rounded-xl",
                    emailValid === true ? "border-green-300 focus:border-green-500" :
                    emailValid === false ? "border-red-300 focus:border-red-500" :
                    "border-gray-200 focus:border-blue-500"
                  )}
                  disabled={loading || sendingEmailCode}
                />
                <Mail className="absolute left-4 top-1/2 -translate-y-1/2 h-4 w-4 text-gray-400" />
                {emailValid !== null && (
                  <div className="absolute right-3 top-1/2 -translate-y-1/2">
                    {emailValid ? (
                      <Check className="h-5 w-5 text-green-500 animate-scale-in" />
                    ) : (
                      <X className="h-5 w-5 text-red-500 animate-scale-in" />
                    )}
                  </div>
                )}
              </div>
            </div>

            {/* 密码输入 - 登录时显示 */}
            {mode === 'login' && (
              <div className="space-y-2">
                <div className="relative">
                  <Input
                    type={showPassword ? "text" : "password"}
                    placeholder="密码"
                    value={password}
                    onChange={(e) => setPassword(e.target.value)}
                    className="h-12 pl-12 pr-12 bg-white/60 backdrop-blur-sm border-2 border-gray-200 focus:border-blue-500 transition-all duration-300 rounded-xl"
                    disabled={loading}
                  />
                  <Lock className="absolute left-4 top-1/2 -translate-y-1/2 h-4 w-4 text-gray-400" />
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    className="absolute right-2 top-1/2 -translate-y-1/2 h-8 w-8 p-0 hover:bg-gray-100/50"
                    onClick={() => setShowPassword(!showPassword)}
                  >
                    {showPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                  </Button>
                </div>
              </div>
            )}

            {/* 邮箱验证码 - 注册和找回密码时显示 */}
            {(mode === 'register' || mode === 'forgot') && (
              <div className="space-y-2">
                <div className="flex gap-3">
                  <Input
                    placeholder="邮箱验证码"
                    value={emailCode}
                    onChange={(e) => setEmailCode(e.target.value.replace(/\D/g, '').slice(0, 6))}
                    className="flex-1 h-12 bg-white/60 backdrop-blur-sm border-2 border-gray-200 focus:border-blue-500 transition-all duration-300 rounded-xl text-center text-lg tracking-wider"
                    maxLength={6}
                    disabled={loading}
                  />
                  <Button
                    type="button"
                    variant="outline"
                    onClick={sendEmailCode}
                    disabled={sendingEmailCode || emailCountdown > 0 || !email || !validateEmail(email)}
                    className="h-12 px-4 min-w-[120px] bg-white/60 backdrop-blur-sm border-2 hover:bg-white/80 transition-all duration-300 rounded-xl"
                  >
                    {sendingEmailCode ? (
                      <>
                        <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                        发送中
                      </>
                    ) : emailCountdown > 0 ? `${emailCountdown}s` : '获取验证码'}
                  </Button>
                </div>
              </div>
            )}

            {/* 密码设置 - 注册和找回密码时显示 */}
            {(mode === 'register' || mode === 'forgot') && (
              <>
                <div className="space-y-2">
                  <div className="relative">
                    <Input
                      type={showPassword ? "text" : "password"}
                      placeholder={mode === 'forgot' ? "新密码" : "设置密码"}
                      value={password}
                      onChange={(e) => setPassword(e.target.value)}
                      className="h-12 pl-12 pr-12 bg-white/60 backdrop-blur-sm border-2 border-gray-200 focus:border-blue-500 transition-all duration-300 rounded-xl"
                      disabled={loading}
                    />
                    <Lock className="absolute left-4 top-1/2 -translate-y-1/2 h-4 w-4 text-gray-400" />
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      className="absolute right-2 top-1/2 -translate-y-1/2 h-8 w-8 p-0 hover:bg-gray-100/50"
                      onClick={() => setShowPassword(!showPassword)}
                    >
                      {showPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                    </Button>
                  </div>
                  
                  {/* 密码强度指示器 */}
                  {passwordStrength && (
                    <div className="space-y-1">
                      <div className="flex items-center justify-between text-xs">
                        <span className="text-gray-600">密码强度</span>
                        <span className={cn(
                          passwordStrength === 'weak' ? 'text-red-500' :
                          passwordStrength === 'medium' ? 'text-yellow-500' :
                          'text-green-500'
                        )}>
                          {passwordStrength === 'weak' ? '弱' :
                           passwordStrength === 'medium' ? '中' : '强'}
                        </span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-1.5">
                        <div 
                          className={cn(
                            "h-1.5 rounded-full transition-all duration-300",
                            passwordStrength === 'weak' ? 'w-1/3 bg-red-500' :
                            passwordStrength === 'medium' ? 'w-2/3 bg-yellow-500' :
                            'w-full bg-green-500'
                          )}
                        ></div>
                      </div>
                    </div>
                  )}
                </div>

                <div className="relative">
                  <Input
                    type={showConfirmPassword ? "text" : "password"}
                    placeholder="确认密码"
                    value={confirmPassword}
                    onChange={(e) => setConfirmPassword(e.target.value)}
                    className="h-12 pl-12 pr-12 bg-white/60 backdrop-blur-sm border-2 border-gray-200 focus:border-blue-500 transition-all duration-300 rounded-xl"
                    disabled={loading}
                  />
                  <Shield className="absolute left-4 top-1/2 -translate-y-1/2 h-4 w-4 text-gray-400" />
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    className="absolute right-2 top-1/2 -translate-y-1/2 h-8 w-8 p-0 hover:bg-gray-100/50"
                    onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                  >
                    {showConfirmPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                  </Button>
                </div>
              </>
            )}

            {/* 图形验证码 - 最后位置 */}
            <div className="space-y-2">
              <div className="flex gap-3">
                <div className="relative flex-1">
                  <Input
                    placeholder="图形验证码"
                    value={captchaCode}
                    onChange={(e) => setCaptchaCode(e.target.value.toUpperCase())}
                    className={cn(
                      "h-12 pl-12 pr-10 bg-white/60 backdrop-blur-sm border-2 transition-all duration-300 rounded-xl text-center text-lg tracking-wider",
                      captchaValid === true ? "border-green-300 focus:border-green-500" :
                      captchaValid === false ? "border-red-300 focus:border-red-500" :
                      "border-gray-200 focus:border-blue-500"
                    )}
                    maxLength={4}
                    disabled={loading || sendingEmailCode}
                  />
                  <Key className="absolute left-4 top-1/2 -translate-y-1/2 h-4 w-4 text-gray-400" />
                  {captchaValid !== null && (
                    <div className="absolute right-3 top-1/2 -translate-y-1/2">
                      {captchaValid ? (
                        <Check className="h-5 w-5 text-green-500 animate-scale-in" />
                      ) : (
                        <X className="h-5 w-5 text-red-500 animate-scale-in" />
                      )}
                    </div>
                  )}
                </div>
                
                <div className="flex items-center gap-2">
                  <img
                    ref={captchaRef}
                    className="h-12 w-28 border-2 border-gray-200 rounded-xl cursor-pointer hover:opacity-80 transition-all duration-300 bg-white/60 backdrop-blur-sm shadow-sm hover:shadow-md"
                    onClick={loadCaptcha}
                    alt="图形验证码"
                  />
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={loadCaptcha}
                    className="h-12 px-3 bg-white/60 backdrop-blur-sm border-2 hover:bg-white/80 transition-all duration-300 rounded-xl"
                  >
                    <RefreshCw className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </div>

            {/* 主按钮 */}
            <Button
              onClick={
                mode === 'login' ? handleLogin : 
                mode === 'register' ? handleRegister : 
                handleForgotPassword
              }
              disabled={
                loading || 
                !email || 
                !validateEmail(email) ||
                (mode === 'login' && (!password || !captchaValid)) ||
                (mode === 'register' && (!emailCode || !password || !confirmPassword || password !== confirmPassword || !captchaValid)) ||
                (mode === 'forgot' && (!emailCode || !password || !confirmPassword || password !== confirmPassword))
              }
              className="w-full h-12 bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white font-medium text-base shadow-lg hover:shadow-xl transition-all duration-300 rounded-xl active:scale-[0.98]"
            >
              {loading ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  {mode === 'login' ? '登录中...' : 
                   mode === 'register' ? '注册中...' : '重置中...'}
                </>
              ) : (
                <>
                  {mode === 'login' ? (
                    <>
                      <User className="h-4 w-4 mr-2" />
                      立即登录
                    </>
                  ) : mode === 'register' ? (
                    <>
                      <UserPlus className="h-4 w-4 mr-2" />
                      立即注册
                    </>
                  ) : (
                    <>
                      <Shield className="h-4 w-4 mr-2" />
                      重置密码
                    </>
                  )}
                  <ArrowRight className="h-4 w-4 ml-2" />
                </>
              )}
            </Button>

            {/* 模式切换 */}
            <div className="flex items-center justify-center space-x-4 text-sm">
              {mode !== 'login' && (
                <Button
                  variant="link"
                  className="text-blue-600 hover:text-blue-700 p-0 h-auto font-medium transition-colors"
                  onClick={() => setMode('login')}
                >
                  返回登录
                </Button>
              )}
              
              {mode === 'login' && (
                <>
                  <Button
                    variant="link"
                    className="text-blue-600 hover:text-blue-700 p-0 h-auto font-medium transition-colors"
                    onClick={() => setMode('register')}
                  >
                    注册账户
                  </Button>
                  <span className="text-gray-300">|</span>
                  <Button
                    variant="link"
                    className="text-blue-600 hover:text-blue-700 p-0 h-auto font-medium transition-colors"
                    onClick={() => setMode('forgot')}
                  >
                    忘记密码
                  </Button>
                </>
              )}
              
              {mode === 'register' && (
                <Button
                  variant="link"
                  className="text-blue-600 hover:text-blue-700 p-0 h-auto font-medium transition-colors"
                  onClick={() => setMode('forgot')}
                >
                  忘记密码
                </Button>
              )}
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}

// 添加自定义动画
const style = document.createElement('style')
style.textContent = `
  @keyframes scale-in {
    from {
      transform: scale(0);
    }
    to {
      transform: scale(1);
    }
  }
  
  .animate-scale-in {
    animation: scale-in 0.3s ease-out;
  }
`
document.head.appendChild(style)