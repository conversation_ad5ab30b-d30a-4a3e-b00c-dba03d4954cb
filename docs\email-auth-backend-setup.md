# 📧 邮箱验证码登录系统 - 后端配置完整指南

## 🎯 系统概述

本系统实现了现代化的邮箱验证码登录，支持：
- ✅ **邮箱验证码登录**：6位数字验证码，10分钟有效期
- ✅ **密码登录**：可选密码设置，支持密码重置
- ✅ **安全特性**：频率限制、密码历史、会话管理
- ✅ **精美邮件**：响应式HTML邮件模板，支持多种验证类型

## 🗄️ 数据库配置

### 1. 执行数据库迁移

```bash
# 在项目根目录执行
psql -h your-host -U your-user -d your-database -f scripts/04-enhanced-auth-tables.sql
```

### 2. 核心数据表说明

```sql
-- 用户表 (扩展)
users (
  email VARCHAR(255) UNIQUE,
  password_hash TEXT,              -- bcrypt哈希密码
  has_password BOOLEAN,            -- 是否设置了密码
  email_verified BOOLEAN,          -- 邮箱是否已验证
  -- ... 其他字段
)

-- 邮箱验证码表
email_codes (
  email VARCHAR(255),
  code VARCHAR(10),                -- 6位数字验证码
  type VARCHAR(20),                -- login, register, reset_password, set_password
  expires_at TIMESTAMP,            -- 过期时间
  used BOOLEAN,                    -- 是否已使用
  attempts INTEGER                 -- 尝试次数
)

-- 用户会话表
user_sessions (
  user_id UUID,
  session_token VARCHAR(128),      -- 会话令牌
  expires_at TIMESTAMP             -- 24小时有效期
)
```

## 🔐 环境变量配置

### 必需环境变量

```bash
# .env.local
DATABASE_URL="postgresql://user:password@host:port/database"
NODE_ENV="development" # 或 "production"

# 邮件服务配置 (选择一种)
# === Nodemailer SMTP ===
SMTP_HOST="smtp.gmail.com"
SMTP_PORT="587"
SMTP_USER="<EMAIL>"
SMTP_PASS="your-app-password"

# === SendGrid ===
SENDGRID_API_KEY="SG.your-api-key"
SENDGRID_FROM_EMAIL="<EMAIL>"

# === 腾讯云邮件 ===
TENCENT_SECRET_ID="your-secret-id"
TENCENT_SECRET_KEY="your-secret-key"
TENCENT_REGION="ap-beijing"

# === 阿里云邮件 ===
ALIBABA_ACCESS_KEY_ID="your-access-key-id"
ALIBABA_ACCESS_KEY_SECRET="your-access-key-secret"
ALIBABA_REGION="cn-hangzhou"

# 应用配置
NEXT_PUBLIC_APP_URL="http://localhost:3000"  # 生产环境改为实际域名
EMAIL_AUTH_SECRET="your-random-secret-32-chars"  # 用于签名验证
```

## 📧 邮件服务集成

### 1. Nodemailer (SMTP) - 推荐

```typescript
// lib/email-service.ts
import nodemailer from 'nodemailer'

const transporter = nodemailer.createTransporter({
  host: process.env.SMTP_HOST,
  port: parseInt(process.env.SMTP_PORT || '587'),
  secure: false, // true for 465, false for other ports
  auth: {
    user: process.env.SMTP_USER,
    pass: process.env.SMTP_PASS,
  },
})

export async function sendEmail(to: string, subject: string, html: string) {
  try {
    const info = await transporter.sendMail({
      from: `"知识商城" <${process.env.SMTP_USER}>`,
      to,
      subject,
      html,
    })
    console.log('✅ 邮件发送成功:', info.messageId)
    return true
  } catch (error) {
    console.error('❌ 邮件发送失败:', error)
    return false
  }
}
```

### 2. SendGrid 集成

```typescript
// lib/sendgrid-service.ts
import sgMail from '@sendgrid/mail'

sgMail.setApiKey(process.env.SENDGRID_API_KEY!)

export async function sendEmailWithSendGrid(to: string, subject: string, html: string) {
  try {
    await sgMail.send({
      to,
      from: process.env.SENDGRID_FROM_EMAIL!,
      subject,
      html,
    })
    console.log('✅ SendGrid邮件发送成功')
    return true
  } catch (error) {
    console.error('❌ SendGrid邮件发送失败:', error)
    return false
  }
}
```

### 3. 腾讯云邮件集成

```typescript
// lib/tencent-email-service.ts
import { ses } from 'tencentcloud-sdk-nodejs'

const SesClient = ses.v20201002.Client
const client = new SesClient({
  credential: {
    secretId: process.env.TENCENT_SECRET_ID!,
    secretKey: process.env.TENCENT_SECRET_KEY!,
  },
  region: process.env.TENCENT_REGION,
  profile: {
    httpProfile: {
      endpoint: "ses.tencentcloudapi.com",
    },
  },
})

export async function sendEmailWithTencent(to: string, subject: string, html: string) {
  try {
    const params = {
      FromEmailAddress: "<EMAIL>",
      Destination: [to],
      Subject: subject,
      ReplyToAddresses: ["<EMAIL>"],
      Template: {
        TemplateData: JSON.stringify({ html }),
      },
    }
    
    const result = await client.SendEmail(params)
    console.log('✅ 腾讯云邮件发送成功:', result.MessageId)
    return true
  } catch (error) {
    console.error('❌ 腾讯云邮件发送失败:', error)
    return false
  }
}
```

## 🔧 集成到认证系统

### 修改 `lib/enhanced-email-auth.ts`

```typescript
// 在 sendEmailCode 函数中集成真实邮件服务
export async function sendEmailCode(
  email: string,
  code: string,
  type: 'login' | 'register' | 'reset_password' | 'set_password' = 'login'
): Promise<boolean> {
  try {
    console.log('📧 准备发送邮箱验证码:', { email, code, type })
    
    // 生成邮件内容
    const emailContent = generateEmailTemplate(code, type, email)
    
    const typeMap = {
      login: '登录验证',
      register: '注册验证', 
      reset_password: '重置密码',
      set_password: '设置密码'
    }
    
    const subject = `知识商城 - ${typeMap[type]}验证码`
    
    // 🔄 替换为真实的邮件发送服务
    if (process.env.NODE_ENV === 'production') {
      // 生产环境：使用真实邮件服务
      
      // 方案1: Nodemailer
      // const result = await sendEmail(email, subject, emailContent)
      
      // 方案2: SendGrid
      // const result = await sendEmailWithSendGrid(email, subject, emailContent)
      
      // 方案3: 腾讯云
      // const result = await sendEmailWithTencent(email, subject, emailContent)
      
      return result
    } else {
      // 开发环境：控制台输出
      console.log(`
╭─────────────────────────────────────╮
│  📧 知识商城 - 邮箱验证码            │
├─────────────────────────────────────┤
│  收件人: ${email.padEnd(25)}       │
│  验证码: ${code}                      │
│  用途: ${typeMap[type]}                       │
│  有效期: 10分钟                      │
╰─────────────────────────────────────╯
      `)
      
      // 可选：在开发环境也发送真实邮件进行测试
      // const result = await sendEmail(email, subject, emailContent)
      // return result
      
      return true // 开发环境模拟成功
    }
  } catch (error) {
    console.error('❌ 邮箱验证码发送失败:', error)
    return false
  }
}
```

## 📦 依赖包安装

### 基础依赖

```bash
# 已有的依赖
npm install bcryptjs @types/bcryptjs
npm install @supabase/supabase-js
```

### 邮件服务依赖 (选择一种)

```bash
# Nodemailer (推荐)
npm install nodemailer @types/nodemailer

# SendGrid
npm install @sendgrid/mail

# 腾讯云
npm install tencentcloud-sdk-nodejs

# 阿里云
npm install @alicloud/pop-core @alicloud/dm20151123
```

## 🔄 定时任务配置

### 1. 清理过期数据

```typescript
// scripts/cleanup-auth-data.ts
import { supabaseAdmin } from '../lib/supabase'

async function cleanupExpiredData() {
  try {
    console.log('🧹 开始清理过期认证数据...')
    
    // 调用数据库函数
    const { data, error } = await supabaseAdmin.rpc('cleanup_expired_auth_data')
    
    if (error) {
      console.error('❌ 清理失败:', error)
      return
    }
    
    console.log(`✅ 清理完成，删除了 ${data} 条过期记录`)
  } catch (error) {
    console.error('❌ 清理异常:', error)
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  cleanupExpiredData()
}

export { cleanupExpiredData }
```

### 2. Vercel Cron Jobs (推荐)

```typescript
// app/api/cron/cleanup/route.ts
import { NextRequest, NextResponse } from 'next/server'
import { cleanupExpiredData } from '@/scripts/cleanup-auth-data'

export async function GET(request: NextRequest) {
  // 验证 Cron Secret (可选，增加安全性)
  const authHeader = request.headers.get('authorization')
  if (authHeader !== `Bearer ${process.env.CRON_SECRET}`) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
  }
  
  try {
    await cleanupExpiredData()
    return NextResponse.json({ success: true, message: 'Cleanup completed' })
  } catch (error) {
    console.error('定时清理失败:', error)
    return NextResponse.json({ error: 'Cleanup failed' }, { status: 500 })
  }
}
```

```json
// vercel.json
{
  "crons": [
    {
      "path": "/api/cron/cleanup",
      "schedule": "0 2 * * *"
    }
  ]
}
```

### 3. 手动执行清理

```bash
# 添加到 package.json scripts
"scripts": {
  "cleanup-auth": "tsx scripts/cleanup-auth-data.ts"
}

# 执行清理
npm run cleanup-auth
```

## 🛡️ 安全配置

### 1. 频率限制

```typescript
// middleware.ts (可选)
import { NextRequest, NextResponse } from 'next/server'

export function middleware(request: NextRequest) {
  // 对认证相关API进行额外的频率限制
  if (request.nextUrl.pathname.startsWith('/api/auth/')) {
    const ip = request.ip || 'unknown'
    
    // 这里可以集成Redis或其他缓存来实现更精确的频率限制
    // 例如：每IP每分钟最多10次认证请求
  }
  
  return NextResponse.next()
}

export const config = {
  matcher: '/api/auth/:path*'
}
```

### 2. 安全响应头

```typescript
// next.config.mjs
/** @type {import('next').NextConfig} */
const nextConfig = {
  async headers() {
    return [
      {
        source: '/api/auth/:path*',
        headers: [
          {
            key: 'X-Content-Type-Options',
            value: 'nosniff',
          },
          {
            key: 'X-Frame-Options',
            value: 'DENY',
          },
          {
            key: 'X-XSS-Protection',
            value: '1; mode=block',
          },
        ],
      },
    ]
  },
}

export default nextConfig
```

## 🚀 部署配置

### 1. Vercel 部署

```bash
# 设置环境变量
vercel env add DATABASE_URL
vercel env add SMTP_HOST
vercel env add SMTP_USER
vercel env add SMTP_PASS
vercel env add EMAIL_AUTH_SECRET

# 部署
vercel --prod
```

### 2. 生产环境检查清单

- [ ] 设置了所有必需的环境变量
- [ ] 配置了邮件服务（SMTP/SendGrid/腾讯云等）
- [ ] 数据库表已创建并建立索引
- [ ] 设置了定时清理任务
- [ ] 测试了验证码发送和接收
- [ ] 测试了密码设置和重置功能
- [ ] 配置了HTTPS（生产环境必须）
- [ ] 设置了正确的CORS策略

## 🧪 测试指南

### 1. 功能测试

```bash
# 测试验证码发送
curl -X POST http://localhost:3000/api/auth/email/send-code \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","type":"login"}'

# 测试验证码登录
curl -X POST http://localhost:3000/api/auth/email/login \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","code":"123456","loginType":"code"}'

# 测试密码登录
curl -X POST http://localhost:3000/api/auth/email/login \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"yourpassword","loginType":"password"}'
```

### 2. 邮件发送测试

```typescript
// scripts/test-email.ts
import { sendEmailCode } from '../lib/enhanced-email-auth'

async function testEmail() {
  const result = await sendEmailCode('<EMAIL>', '123456', 'login')
  console.log('测试结果:', result)
}

testEmail()
```

## 📊 监控和日志

### 1. 登录统计

```sql
-- 查看登录统计
SELECT 
  auth_type,
  status,
  COUNT(*) as count,
  DATE_TRUNC('day', created_at) as date
FROM login_logs 
WHERE created_at >= NOW() - INTERVAL '7 days'
GROUP BY auth_type, status, DATE_TRUNC('day', created_at)
ORDER BY date DESC;
```

### 2. 验证码使用情况

```sql
-- 查看验证码使用情况
SELECT 
  type,
  used,
  COUNT(*) as count,
  AVG(attempts) as avg_attempts
FROM email_codes 
WHERE created_at >= NOW() - INTERVAL '7 days'
GROUP BY type, used;
```

## 🔧 故障排除

### 常见问题

1. **验证码收不到**
   - 检查邮件服务配置
   - 查看开发者控制台日志
   - 检查垃圾邮件文件夹

2. **验证码过期**
   - 确认时区设置正确
   - 检查服务器时间

3. **密码设置失败**
   - 检查密码强度验证
   - 确认验证码有效性

4. **会话过期**
   - 检查session_token配置
   - 确认Cookie设置正确

---

**配置完成后，您将拥有一个功能完整、安全可靠的邮箱验证码登录系统！** 🎉