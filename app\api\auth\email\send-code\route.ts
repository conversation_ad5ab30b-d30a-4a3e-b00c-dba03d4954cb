import { NextRequest, NextResponse } from "next/server"
import { generateEmailCode, sendEmailCode, validateEmail, checkEmailRateLimit } from "@/lib/enhanced-email-auth"
import { supabaseAdmin } from "@/lib/supabase"

/**
 * 发送邮箱验证码 API
 * POST /api/auth/email/send-code
 */
export async function POST(request: NextRequest) {
  try {
    const { email, type = 'login' } = await request.json()
    
    // 验证邮箱格式
    if (!email || !validateEmail(email)) {
      return NextResponse.json({
        success: false,
        error: "请输入有效的邮箱地址"
      }, { status: 400 })
    }
    
    // 验证类型参数
    const validTypes = ['login', 'register', 'reset_password', 'set_password']
    if (!validTypes.includes(type)) {
      return NextResponse.json({
        success: false,
        error: "无效的验证码类型"
      }, { status: 400 })
    }
    
    console.log('📧 收到验证码发送请求:', { email, type })
    
    // 检查频率限制
    const canSend = await checkEmailRateLimit(email)
    if (!canSend) {
      return NextResponse.json({
        success: false,
        error: "发送过于频繁，请1小时后再试"
      }, { status: 429 })
    }
    
    // 🔧 修复：对于注册类型，检查邮箱是否已存在
    if (type === 'register') {
      const { data: existingUser } = await supabaseAdmin
        .from('users')
        .select('id, created_at, nickname')
        .eq('email', email)
        .single()
      
      if (existingUser) {
        console.log('⚠️ 注册验证码请求失败 - 邮箱已存在:', { 
          email, 
          userId: existingUser.id, 
          nickname: existingUser.nickname,
          createdAt: existingUser.created_at 
        })
        return NextResponse.json({
          success: false,
          error: "该邮箱已被注册，请直接登录或使用其他邮箱"
        }, { status: 400 })
      }
      
      console.log('✅ 邮箱可用，允许发送注册验证码')
    }
    
    // 对于重置密码和设置密码，检查用户是否存在
    if (type === 'reset_password' || type === 'set_password') {
      const { data: user } = await supabaseAdmin
        .from('users')
        .select('id, has_password')
        .eq('email', email)
        .single()
      
      if (!user) {
        return NextResponse.json({
          success: false,
          error: "该邮箱未注册"
        }, { status: 404 })
      }
      
      if (type === 'set_password' && user.has_password) {
        return NextResponse.json({
          success: false,
          error: "该账户已设置密码，如需修改请使用重置密码功能"
        }, { status: 400 })
      }
      
      if (type === 'reset_password' && !user.has_password) {
        return NextResponse.json({
          success: false,
          error: "该账户尚未设置密码，请使用验证码登录后设置密码"
        }, { status: 400 })
      }
    }
    
    // 生成验证码
    const { code, expiresAt } = await generateEmailCode(email, type)
    
    // 发送邮件
    const emailSent = await sendEmailCode(email, code, type)
    
    if (!emailSent) {
      return NextResponse.json({
        success: false,
        error: "邮件发送失败，请稍后重试"
      }, { status: 500 })
    }
    
    // 记录日志
    const clientIP = request.ip || 
                     request.headers.get("x-forwarded-for")?.split(',')[0] || 
                     "unknown"
    
    await supabaseAdmin
      .from('login_logs')
      .insert({
        email,
        auth_type: `${type}_code_sent`,
        status: 'success',
        ip_address: clientIP,
        user_agent: request.headers.get('user-agent') || 'unknown',
        created_at: new Date().toISOString()
      })
    
    console.log('✅ 验证码发送成功:', { 
      email, 
      type,
      expiresAt: expiresAt.toISOString() 
    })
    
    const typeMessages = {
      login: '登录验证码已发送到您的邮箱',
      register: '注册验证码已发送到您的邮箱',
      reset_password: '重置密码验证码已发送到您的邮箱',
      set_password: '设置密码验证码已发送到您的邮箱'
    }
    
    return NextResponse.json({
      success: true,
      message: typeMessages[type as keyof typeof typeMessages],
      expiresIn: 10 * 60 * 1000, // 10分钟，毫秒
      // 在开发环境下返回验证码用于测试
      ...(process.env.NODE_ENV === 'development' && {
        code, // 仅开发环境返回
        developmentMode: true
      })
    })
    
  } catch (error) {
    console.error('❌ 验证码发送请求失败:', error)
    
    return NextResponse.json({
      success: false,
      error: "请求处理失败，请稍后重试"
    }, { status: 500 })
  }
}

/**
 * 获取邮箱验证状态
 * GET /api/auth/email/send-code?email=xxx&type=xxx
 */
export async function GET(request: NextRequest) {
  try {
    const searchParams = request.nextUrl.searchParams
    const email = searchParams.get('email')
    const type = searchParams.get('type') || 'login'
    
    if (!email || !validateEmail(email)) {
      return NextResponse.json({
        success: false,
        error: "请提供有效的邮箱地址"
      }, { status: 400 })
    }
    
    // 检查是否有未使用的有效验证码
    const { data: activeCodes, error } = await supabaseAdmin
      .from('email_codes')
      .select('expires_at, attempts')
      .eq('email', email)
      .eq('type', type)
      .eq('used', false)
      .gt('expires_at', new Date().toISOString())
      .order('created_at', { ascending: false })
      .limit(1)
    
    if (error) {
      console.error('❌ 查询验证码状态失败:', error)
      return NextResponse.json({
        success: false,
        error: "查询失败"
      }, { status: 500 })
    }
    
    const hasActiveCode = activeCodes && activeCodes.length > 0
    const nextExpiry = hasActiveCode ? activeCodes[0].expires_at : null
    const attempts = hasActiveCode ? activeCodes[0].attempts : 0
    
    // 检查用户是否存在及密码状态
    const { data: user } = await supabaseAdmin
      .from('users')
      .select('id, email, nickname, has_password, created_at')
      .eq('email', email)
      .single()
    
    return NextResponse.json({
      success: true,
      data: {
        email,
        type,
        hasActiveCode,
        nextExpiry,
        attempts,
        canRetry: attempts < 5,
        isRegistered: !!user,
        hasPassword: user?.has_password || false,
        userInfo: user ? {
          nickname: user.nickname,
          memberSince: user.created_at
        } : null
      }
    })
    
  } catch (error) {
    console.error('❌ 获取验证状态失败:', error)
    
    return NextResponse.json({
      success: false,
      error: "查询失败"
    }, { status: 500 })
  }
}