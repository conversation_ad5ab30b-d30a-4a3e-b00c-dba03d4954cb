"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON>, <PERSON>alogContent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Ta<PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Separator } from "@/components/ui/separator"
import { Badge } from "@/components/ui/badge"
import { 
  Mail, 
  Send, 
  CheckCircle, 
  Clock, 
  AlertCircle, 
  Eye, 
  EyeOff, 
  Key, 
  Shield,
  User,
  Lock,
  UserPlus
} from "lucide-react"
import { useToast } from "@/hooks/use-toast"

interface EnhancedEmailLoginDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  onLoginSuccess: (userInfo: any) => void
}

export function EnhancedEmailLoginDialog({ open, onOpenChange, onLoginSuccess }: EnhancedEmailLoginDialogProps) {
  // 基础状态
  const [email, setEmail] = useState("")
  const [password, setPassword] = useState("")
  const [code, setCode] = useState("")
  const [newPassword, setNewPassword] = useState("")
  const [showPassword, setShowPassword] = useState(false)
  const [showNewPassword, setShowNewPassword] = useState(false)
  
  // 加载状态
  const [loading, setLoading] = useState(false)
  const [sendingCode, setSendingCode] = useState(false)
  
  // 验证码相关状态
  const [codeSent, setCodeSent] = useState(false)
  const [countdown, setCountdown] = useState(0)
  const [codeType, setCodeType] = useState<'login' | 'set_password' | 'reset_password'>('login')
  
  // 用户状态
  const [userInfo, setUserInfo] = useState<any>(null)
  const [isNewUser, setIsNewUser] = useState(false)
  
  // UI状态
  const [activeTab, setActiveTab] = useState<'code' | 'password'>('code')
  const [showPasswordSetup, setShowPasswordSetup] = useState(false)
  
  const { toast } = useToast()

  // 验证邮箱格式
  const validateEmail = (email: string) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    return emailRegex.test(email)
  }

  // 验证密码强度
  const validatePassword = (password: string) => {
    if (password.length < 6) {
      return { valid: false, error: '密码长度至少6位' }
    }
    return { valid: true }
  }

  // 发送验证码
  const sendCode = async (type: 'login' | 'set_password' | 'reset_password' = 'login') => {
    if (!email || !validateEmail(email)) {
      toast({
        title: "邮箱格式错误",
        description: "请输入正确的邮箱地址",
        variant: "destructive",
      })
      return
    }

    setSendingCode(true)
    setCodeType(type)
    
    try {
      const response = await fetch('/api/auth/email/send-code', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email, type })
      })
      
      const data = await response.json()
      
      if (data.success) {
        setCodeSent(true)
        setCountdown(60) // 60秒倒计时
        
        // 开发模式下显示验证码
        if (data.developmentMode && data.code) {
          console.log('🔍 开发模式验证码:', data.code)
        }
        
        // 启动倒计时
        const timer = setInterval(() => {
          setCountdown(prev => {
            if (prev <= 1) {
              clearInterval(timer)
              return 0
            }
            return prev - 1
          })
        }, 1000)
        
        const typeMessages = {
          login: '登录验证码已发送',
          set_password: '设置密码验证码已发送',
          reset_password: '重置密码验证码已发送'
        }
        
        toast({
          title: "验证码发送成功",
          description: typeMessages[type] + "，请查收您的邮箱",
        })
      } else {
        toast({
          title: "发送失败",
          description: data.error || "请稍后重试",
          variant: "destructive",
        })
      }
    } catch (error) {
      console.error('发送验证码失败:', error)
      toast({
        title: "网络错误",
        description: "请检查网络连接后重试",
        variant: "destructive",
      })
    } finally {
      setSendingCode(false)
    }
  }

  // 验证码登录
  const loginWithCode = async () => {
    if (!email || !code) {
      toast({
        title: "信息不完整",
        description: "请输入邮箱和验证码",
        variant: "destructive",
      })
      return
    }

    setLoading(true)
    
    try {
      const response = await fetch('/api/auth/email/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ 
          email, 
          code, 
          loginType: 'code' 
        })
      })
      
      const data = await response.json()
      
      if (data.success) {
        setUserInfo(data.user)
        setIsNewUser(data.isNewUser)
        
        // 检查是否需要设置密码
        if (!data.user.has_password) {
          setShowPasswordSetup(true)
          toast({
            title: "登录成功",
            description: "建议设置密码以便下次快速登录",
          })
        } else {
          // 直接完成登录
          onLoginSuccess(data.user)
          onOpenChange(false)
          toast({
            title: "登录成功",
            description: `欢迎 ${data.user.nickname || data.user.username}！`,
          })
        }
      } else {
        toast({
          title: "登录失败",
          description: data.error || "验证码错误或已过期",
          variant: "destructive",
        })
      }
    } catch (error) {
      console.error('验证码登录失败:', error)
      toast({
        title: "登录异常",
        description: "请重新尝试",
        variant: "destructive",
      })
    } finally {
      setLoading(false)
    }
  }

  // 密码登录
  const loginWithPassword = async () => {
    if (!email || !password) {
      toast({
        title: "信息不完整",
        description: "请输入邮箱和密码",
        variant: "destructive",
      })
      return
    }

    setLoading(true)
    
    try {
      const response = await fetch('/api/auth/email/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ 
          email, 
          password, 
          loginType: 'password' 
        })
      })
      
      const data = await response.json()
      
      if (data.success) {
        onLoginSuccess(data.user)
        onOpenChange(false)
        toast({
          title: "登录成功",
          description: `欢迎回来 ${data.user.nickname || data.user.username}！`,
        })
      } else {
        toast({
          title: "登录失败",
          description: data.error || "邮箱或密码错误",
          variant: "destructive",
        })
      }
    } catch (error) {
      console.error('密码登录失败:', error)
      toast({
        title: "登录异常",
        description: "请重新尝试",
        variant: "destructive",
      })
    } finally {
      setLoading(false)
    }
  }

  // 设置密码
  const setupPassword = async () => {
    if (!code || !newPassword) {
      toast({
        title: "信息不完整",
        description: "请输入验证码和新密码",
        variant: "destructive",
      })
      return
    }

    const passwordValidation = validatePassword(newPassword)
    if (!passwordValidation.valid) {
      toast({
        title: "密码格式错误",
        description: passwordValidation.error,
        variant: "destructive",
      })
      return
    }

    setLoading(true)
    
    try {
      const response = await fetch('/api/auth/email/password', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ 
          email, 
          code, 
          password: newPassword, 
          action: codeType === 'reset_password' ? 'reset' : 'set'
        })
      })
      
      const data = await response.json()
      
      if (data.success) {
        // 完成登录流程
        if (userInfo) {
          onLoginSuccess({ ...userInfo, has_password: true })
        }
        onOpenChange(false)
        toast({
          title: "操作成功",
          description: data.message,
        })
      } else {
        toast({
          title: "操作失败",
          description: data.error || "请重新尝试",
          variant: "destructive",
        })
      }
    } catch (error) {
      console.error('密码设置失败:', error)
      toast({
        title: "操作异常",
        description: "请重新尝试",
        variant: "destructive",
      })
    } finally {
      setLoading(false)
    }
  }

  // 重置表单状态
  const resetForm = () => {
    setEmail("")
    setPassword("")
    setCode("")
    setNewPassword("")
    setCodeSent(false)
    setCountdown(0)
    setUserInfo(null)
    setIsNewUser(false)
    setShowPasswordSetup(false)
    setActiveTab('code')
    setShowPassword(false)
    setShowNewPassword(false)
    setLoading(false)
    setSendingCode(false)
  }

  // 监听对话框关闭
  const handleOpenChange = (isOpen: boolean) => {
    if (!isOpen) {
      resetForm()
    }
    onOpenChange(isOpen)
  }

  return (
    <Dialog open={open} onOpenChange={handleOpenChange}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center">
            <Mail className="h-5 w-5 mr-2 text-blue-600" />
            邮箱登录
          </DialogTitle>
          <DialogDescription>
            选择验证码登录或密码登录方式
          </DialogDescription>
        </DialogHeader>

        {!showPasswordSetup ? (
          // 主登录界面
          <Tabs value={activeTab} onValueChange={(value) => setActiveTab(value as 'code' | 'password')}>
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="code" className="flex items-center">
                <Shield className="h-4 w-4 mr-2" />
                验证码登录
              </TabsTrigger>
              <TabsTrigger value="password" className="flex items-center">
                <Key className="h-4 w-4 mr-2" />
                密码登录
              </TabsTrigger>
            </TabsList>

            {/* 邮箱输入 - 公共部分 */}
            <div className="space-y-4 mt-4">
              <div className="space-y-2">
                <Label htmlFor="email">邮箱地址</Label>
                <Input
                  id="email"
                  type="email"
                  placeholder="请输入您的邮箱地址"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  disabled={loading || sendingCode}
                />
              </div>

              <TabsContent value="code" className="space-y-4">
                {!codeSent ? (
                  // 发送验证码阶段
                  <Button 
                    onClick={() => sendCode('login')} 
                    disabled={sendingCode || !email}
                    className="w-full"
                  >
                    {sendingCode ? (
                      <>
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                        发送中...
                      </>
                    ) : (
                      <>
                        <Send className="h-4 w-4 mr-2" />
                        发送验证码
                      </>
                    )}
                  </Button>
                ) : (
                  // 输入验证码阶段
                  <div className="space-y-4">
                    <div className="bg-green-50 border border-green-200 rounded-lg p-3 mb-3">
                      <div className="flex items-center">
                        <CheckCircle className="h-4 w-4 text-green-600 mr-2" />
                        <p className="text-sm text-green-700">
                          验证码已发送到 <strong>{email}</strong>
                        </p>
                      </div>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="code">验证码</Label>
                      <Input
                        id="code"
                        placeholder="请输入6位验证码"
                        value={code}
                        onChange={(e) => setCode(e.target.value.replace(/\D/g, '').slice(0, 6))}
                        maxLength={6}
                        className="text-center text-lg tracking-wider font-mono"
                      />
                    </div>

                    <Button 
                      onClick={loginWithCode} 
                      disabled={loading || code.length !== 6}
                      className="w-full"
                    >
                      {loading ? (
                        <>
                          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                          登录中...
                        </>
                      ) : (
                        <>
                          <User className="h-4 w-4 mr-2" />
                          验证登录
                        </>
                      )}
                    </Button>

                    <div className="flex justify-between items-center text-sm">
                      <Button 
                        variant="ghost" 
                        size="sm"
                        onClick={() => setCodeSent(false)}
                      >
                        更换邮箱
                      </Button>
                      
                      <Button 
                        variant="ghost" 
                        size="sm"
                        onClick={() => sendCode('login')}
                        disabled={countdown > 0}
                      >
                        {countdown > 0 ? `重新发送 (${countdown}s)` : '重新发送'}
                      </Button>
                    </div>
                  </div>
                )}
              </TabsContent>

              <TabsContent value="password" className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="password">密码</Label>
                  <div className="relative">
                    <Input
                      id="password"
                      type={showPassword ? "text" : "password"}
                      placeholder="请输入您的密码"
                      value={password}
                      onChange={(e) => setPassword(e.target.value)}
                      disabled={loading}
                    />
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                      onClick={() => setShowPassword(!showPassword)}
                    >
                      {showPassword ? (
                        <EyeOff className="h-4 w-4" />
                      ) : (
                        <Eye className="h-4 w-4" />
                      )}
                    </Button>
                  </div>
                </div>

                <Button 
                  onClick={loginWithPassword} 
                  disabled={loading || !email || !password}
                  className="w-full"
                >
                  {loading ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                      登录中...
                    </>
                  ) : (
                    <>
                      <Lock className="h-4 w-4 mr-2" />
                      密码登录
                    </>
                  )}
                </Button>

                <div className="text-center">
                  <Button 
                    variant="link" 
                    size="sm"
                    onClick={() => {
                      sendCode('reset_password')
                      setShowPasswordSetup(true)
                    }}
                    className="text-sm text-blue-600"
                  >
                    忘记密码？
                  </Button>
                </div>
              </TabsContent>
            </div>
          </Tabs>
        ) : (
          // 密码设置界面
          <Card>
            <CardHeader className="text-center">
              <CardTitle className="flex items-center justify-center">
                {codeType === 'reset_password' ? (
                  <>
                    <Shield className="h-5 w-5 mr-2 text-orange-600" />
                    重置密码
                  </>
                ) : (
                  <>
                    <UserPlus className="h-5 w-5 mr-2 text-green-600" />
                    设置密码
                  </>
                )}
              </CardTitle>
              <CardDescription>
                {codeType === 'reset_password' 
                  ? '请输入验证码和新密码来重置您的密码'
                  : '为您的账户设置密码，下次可以直接使用密码登录'
                }
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {!codeSent && (
                <Button 
                  onClick={() => sendCode(codeType)} 
                  disabled={sendingCode}
                  className="w-full"
                >
                  {sendingCode ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                      发送中...
                    </>
                  ) : (
                    <>
                      <Send className="h-4 w-4 mr-2" />
                      获取验证码
                    </>
                  )}
                </Button>
              )}

              {codeSent && (
                <>
                  <div className="space-y-2">
                    <Label htmlFor="setupCode">验证码</Label>
                    <Input
                      id="setupCode"
                      placeholder="请输入6位验证码"
                      value={code}
                      onChange={(e) => setCode(e.target.value.replace(/\D/g, '').slice(0, 6))}
                      maxLength={6}
                      className="text-center text-lg tracking-wider font-mono"
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="newPassword">
                      {codeType === 'reset_password' ? '新密码' : '设置密码'}
                    </Label>
                    <div className="relative">
                      <Input
                        id="newPassword"
                        type={showNewPassword ? "text" : "password"}
                        placeholder="请输入密码（至少6位）"
                        value={newPassword}
                        onChange={(e) => setNewPassword(e.target.value)}
                        disabled={loading}
                      />
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                        onClick={() => setShowNewPassword(!showNewPassword)}
                      >
                        {showNewPassword ? (
                          <EyeOff className="h-4 w-4" />
                        ) : (
                          <Eye className="h-4 w-4" />
                        )}
                      </Button>
                    </div>
                  </div>

                  <Button 
                    onClick={setupPassword} 
                    disabled={loading || code.length !== 6 || !newPassword}
                    className="w-full"
                  >
                    {loading ? (
                      <>
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                        设置中...
                      </>
                    ) : (
                      <>
                        <Key className="h-4 w-4 mr-2" />
                        {codeType === 'reset_password' ? '重置密码' : '设置密码'}
                      </>
                    )}
                  </Button>

                  {codeType !== 'reset_password' && (
                    <Button 
                      variant="outline"
                      onClick={() => {
                        if (userInfo) {
                          onLoginSuccess(userInfo)
                        }
                        onOpenChange(false)
                      }}
                      className="w-full"
                    >
                      跳过，稍后设置
                    </Button>
                  )}
                </>
              )}
            </CardContent>
          </Card>
        )}

        {/* 安全提示 */}
        {!showPasswordSetup && (
          <div className="text-xs text-gray-500 space-y-1 border-t pt-4">
            <p>🔒 安全提示：</p>
            <ul className="space-y-1 ml-4">
              <li>• 验证码10分钟内有效，请及时使用</li>
              <li>• 建议设置强密码，提升账户安全性</li>
              <li>• 首次登录将自动创建账户</li>
              <li>• 我们不会向您索要密码或验证码</li>
            </ul>
          </div>
        )}
      </DialogContent>
    </Dialog>
  )
}