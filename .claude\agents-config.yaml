# 🤖 SuperClaude 中文关键词Agent自动分配配置
# 知识商城项目专用配置 - 针对Next.js + TypeScript + Supabase技术栈

# 配置说明：
# - confidence: 匹配置信度 (0.0-1.0)
# - priority: 优先级 (1-10, 数字越大优先级越高)
# - auto_flags: 自动激活的标志
# - context_boost: 上下文相关性加成

agent_mapping:
  # 🎨 前端开发类 (Frontend Development)
  frontend-developer:
    keywords:
      primary:
        - 组件
        - 界面
        - UI组件
        - React组件
        - 前端
        - 页面
        - 布局
        - 响应式
        - 样式
        - CSS
        - Tailwind
        - shadcn
      secondary:
        - 用户界面
        - 交互
        - 动画
        - 主题
        - 深色模式
        - 浅色模式
        - 移动端
        - 桌面端
    confidence: 0.9
    priority: 8
    auto_flags: ["--magic", "--c7"]
    context_boost:
      file_patterns: ["*.tsx", "*.jsx", "components/*", "app/*page.tsx"]
      project_keywords: ["shadcn", "radix", "tailwind"]

  # 🔧 后端架构类 (Backend Architecture)
  backend-architect:
    keywords:
      primary:
        - API
        - 接口
        - 后端
        - 数据库
        - 服务器
        - 路由
        - 中间件
        - 认证
        - 权限
        - PostgreSQL
        - Supabase
      secondary:
        - 数据模型
        - 数据表
        - 查询
        - 事务
        - 连接池
        - 缓存
        - 性能优化
        - 扩展性
    confidence: 0.95
    priority: 9
    auto_flags: ["--seq", "--c7"]
    context_boost:
      file_patterns: ["*/api/*", "lib/*.ts", "scripts/*.sql"]
      project_keywords: ["supabase", "postgresql", "nextjs"]

  # 🔒 安全审计类 (Security Auditor)
  security-auditor:
    keywords:
      primary:
        - 安全
        - 漏洞
        - 认证
        - 授权
        - 加密
        - 密钥
        - JWT
        - 哈希
        - 防护
      secondary:
        - 注入攻击
        - XSS
        - CSRF
        - SQL注入
        - 身份验证
        - 权限控制
        - 数据保护
        - 合规性
    confidence: 0.92
    priority: 10
    auto_flags: ["--seq", "--validate", "--safe-mode"]
    context_boost:
      file_patterns: ["lib/auth.ts", "*/auth/*", "lib/security*"]
      project_keywords: ["bcrypt", "jwt", "auth"]

  # 📊 数据库优化类 (Database Optimizer)
  database-optimizer:
    keywords:
      primary:
        - 数据库优化
        - 查询优化
        - 索引
        - 性能调优
        - SQL优化
        - 慢查询
      secondary:
        - 数据库设计
        - 表结构
        - 关联查询
        - 分页查询
        - 数据库连接
        - 查询计划
    confidence: 0.88
    priority: 7
    auto_flags: ["--seq", "--think"]
    context_boost:
      file_patterns: ["scripts/*.sql", "lib/database.ts"]
      project_keywords: ["postgresql", "supabase"]

  # 🧪 测试自动化类 (Test Automator)
  test-automator:
    keywords:
      primary:
        - 测试
        - 单元测试
        - 集成测试
        - 端到端测试
        - 自动化测试
        - 测试用例
      secondary:
        - 测试覆盖率
        - 测试框架
        - Mock
        - 断言
        - 测试数据
        - 回归测试
    confidence: 0.85
    priority: 6
    auto_flags: ["--play", "--validate"]
    context_boost:
      file_patterns: ["*.test.ts", "*.spec.ts", "__tests__/*"]

  # ⚡ 性能工程师类 (Performance Engineer)
  performance-engineer:
    keywords:
      primary:
        - 性能优化
        - 性能测试
        - 加载速度
        - 响应时间
        - 内存优化
        - 缓存
      secondary:
        - 首屏加载
        - 代码分割
        - 懒加载
        - 压缩
        - CDN
        - 监控
        - 指标
    confidence: 0.87
    priority: 7
    auto_flags: ["--play", "--think", "--validate"]
    context_boost:
      project_keywords: ["next", "vercel", "optimization"]

  # 🔍 错误侦探类 (Error Detective)
  error-detective:
    keywords:
      primary:
        - 错误
        - Bug
        - 调试
        - 故障排除
        - 问题诊断
        - 日志分析
      secondary:
        - 异常处理
        - 错误追踪
        - 堆栈跟踪
        - 调试信息
        - 监控告警
    confidence: 0.90
    priority: 8
    auto_flags: ["--seq", "--think"]

  # 📝 文档专家类 (API Documenter / Scribe)
  api-documenter:
    keywords:
      primary:
        - 文档
        - 说明
        - 注释
        - API文档
        - 用户手册
        - 开发文档
      secondary:
        - README
        - 接口说明
        - 使用指南
        - 示例代码
        - 变更日志
    confidence: 0.80
    priority: 5
    auto_flags: ["--persona-scribe=zh", "--c7"]
    context_boost:
      file_patterns: ["*.md", "docs/*", "README*"]

  # 🚀 部署工程师类 (Deployment Engineer)
  deployment-engineer:
    keywords:
      primary:
        - 部署
        - 发布
        - 构建
        - CI/CD
        - 生产环境
        - 配置
      secondary:
        - Docker
        - 容器化
        - 环境变量
        - 版本控制
        - 回滚
        - 监控
    confidence: 0.83
    priority: 6
    auto_flags: ["--validate", "--safe-mode"]
    context_boost:
      file_patterns: ["next.config.*", "package.json", ".env*"]

  # 🏗️ 系统架构师类 (Architect)
  architect:
    keywords:
      primary:
        - 架构
        - 系统设计
        - 模块化
        - 可扩展性
        - 设计模式
        - 重构
      secondary:
        - 技术选型
        - 架构决策
        - 系统集成
        - 微服务
        - 模块依赖
        - 代码结构
    confidence: 0.95
    priority: 9
    auto_flags: ["--seq", "--ultrathink", "--c7"]
    context_boost:
      project_keywords: ["architecture", "design", "pattern"]

  # 🧹 代码重构师类 (Refactorer)
  refactorer:
    keywords:
      primary:
        - 重构
        - 代码优化
        - 代码清理
        - 技术债务
        - 代码质量
      secondary:
        - 代码规范
        - 最佳实践
        - 代码复用
        - 简化代码
        - 消除重复
    confidence: 0.85
    priority: 6
    auto_flags: ["--seq", "--validate"]

# 🔄 特殊模式配置
special_modes:
  # 迭代改进模式
  iterative_improvement:
    keywords: [改进, 优化, 完善, 增强, 提升, 迭代, 逐步, 循环]
    auto_flags: ["--loop", "--seq"]
    confidence: 0.90

  # Wave模式触发
  wave_mode:
    keywords: [全面, 系统性, 彻底, 企业级, 大规模, 多阶段, 渐进式, 综合]
    auto_flags: ["--wave-mode", "--adaptive-waves"]
    confidence: 0.92

  # 子代理分配模式
  delegation_mode:
    keywords: [并行, 分布式, 多任务, 批量, 大量文件, 多模块]
    auto_flags: ["--delegate", "--parallel-dirs"]
    confidence: 0.85

# 🎯 项目特定配置 (知识商城专用)
project_specific:
  # 教程管理相关
  tutorial_management:
    keywords: [教程, 课程, 内容, 章节, 学习, 教学]
    preferred_agent: "frontend-developer"
    confidence: 0.88
    context_boost:
      file_patterns: ["*tutorial*", "*course*", "*content*"]

  # 密钥系统相关
  key_system:
    keywords: [密钥, 解锁, 验证, 购买, 支付]
    preferred_agent: "security-auditor"
    confidence: 0.92
    context_boost:
      file_patterns: ["*key*", "*unlock*", "*verify*"]

  # 用户管理相关
  user_management:
    keywords: [用户, 登录, 注册, 认证, 权限]
    preferred_agent: "security-auditor"
    confidence: 0.90
    context_boost:
      file_patterns: ["*user*", "*auth*", "*login*"]

  # 管理后台相关
  admin_panel:
    keywords: [管理, 后台, 管理员, 控制台, 仪表板]
    preferred_agent: "backend-architect"
    confidence: 0.87
    context_boost:
      file_patterns: ["*admin*", "*dashboard*", "*panel*"]

# ⚙️ 配置参数
settings:
  # 匹配阈值
  confidence_threshold: 0.75
  
  # 多关键词权重计算
  multiple_keywords_boost: 0.15
  
  # 上下文相关性权重
  context_relevance_weight: 0.25
  
  # 项目特定权重
  project_specific_weight: 0.20
  
  # 自动分配启用
  auto_assignment_enabled: true
  
  # 默认回退Agent
  fallback_agent: "general-purpose"
  
  # 日志级别
  log_level: "info"