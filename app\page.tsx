"use client"

import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import {
  Key,
  BookOpen,
  CheckCircle,
  AlertCircle,
  Lock,
  Unlock,
  Search,
  Filter,
  Clock,
  MessageCircle,
  Library,
  RefreshCw,
  Wifi,
  WifiOff,
  ChevronLeft,
  ChevronRight,
} from "lucide-react"
import { useToast } from "@/hooks/use-toast"
import { Progress } from "@/components/ui/progress"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { CleanEmailLoginDialog } from "@/components/clean-email-login-dialog"
import { AnnouncementBell } from "@/components/AnnouncementBell"
import { useTutorialsSimple } from "@/hooks/use-simple-refresh"
import { LoadingAnimation } from "@/components/ui/loading-animation"
import { CubeSpinner } from "@/components/ui/cube-spinner"

interface Tutorial {
  id: number
  title: string
  description: string
  content: string
  category_name: string
  tags: string[]
  price: number
  created_at: string
  is_unlocked: boolean
  // 添加学习进度相关字段
  progress?: number // 0-100
  learning_status?: "not_started" | "in_progress" | "completed"
  last_accessed?: string
  reading_time?: number // in minutes
  difficulty?: "beginner" | "intermediate" | "advanced"
  rating?: number // 1-5
}

interface Category {
  id: number
  name: string
  description: string
}

interface KeyValidationResult {
  success: boolean
  tutorial?: Tutorial
  user_unlocks?: Tutorial[]
  error?: string
}

export default function HomePage() {
  const [keyCode, setKeyCode] = useState("")
  const [loading, setLoading] = useState(false)
  const [refreshing, setRefreshing] = useState(false) // 新增：刷新状态
  const [validationResult, setValidationResult] = useState<KeyValidationResult | null>(null)
  const [categories, setCategories] = useState<Category[]>([])
  const [selectedCategory, setSelectedCategory] = useState<string>("all")
  const [searchQuery, setSearchQuery] = useState("")
  const [userUnlocks, setUserUnlocks] = useState<Tutorial[]>([])
  const [tutorialProgressData, setTutorialProgressData] = useState<Map<number, any>>(new Map())
  const [showEmailLogin, setShowEmailLogin] = useState(false)
  const [recentlyUnlockedId, setRecentlyUnlockedId] = useState<number | null>(null)
  const [currentUser, setCurrentUser] = useState<any>(null)
  // 新增：分页状态
  const [currentPage, setCurrentPage] = useState(1)
  const tutorialsPerPage = 3 // 每页显示3个教程
  const { toast } = useToast()
  const router = useRouter()

  // 🚀 简化版教程数据刷新 (修复无限循环)
  const {
    data: tutorialsResponse,
    loading: tutorialsLoading,
    error: tutorialsError,
    refresh: forceRefresh
  } = useTutorialsSimple(true)

  // 从响应中提取教程数据
  const tutorials = tutorialsResponse?.data || []

  // 🔧 添加调试信息
  console.log('🔍 教程数据调试:', {
    tutorialsResponse,
    tutorials,
    tutorialsLength: tutorials.length,
    firstTutorial: tutorials[0],
    tutorialsLoading,
    tutorialsError
  })

  useEffect(() => {
    loadCategories()
    loadUserUnlocks()
    checkSavedUser()

    // 添加页面可见性变化监听器
    const handleVisibilityChange = () => {
      if (document.visibilityState === 'visible') {
        // 页面变为可见时，刷新用户解锁状态
        console.log('🔄 页面变为可见，刷新用户解锁状态')
        loadUserUnlocks()
      }
    }

    document.addEventListener('visibilitychange', handleVisibilityChange)

    // 设置定期刷新用户解锁状态（每30秒）
    const interval = setInterval(() => {
      if (document.visibilityState === 'visible') {
        console.log('⏰ 定期刷新用户解锁状态')
        loadUserUnlocks()
      }
    }, 30000) // 30秒刷新一次

    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange)
      clearInterval(interval)
    }
  }, [])

  // 监听教程数据错误
  useEffect(() => {
    if (tutorialsError) {
      console.error('教程数据加载失败:', tutorialsError)
      toast({
        title: "数据加载失败",
        description: "正在尝试重新连接...",
        variant: "destructive"
      })
    }
  }, [tutorialsError, toast])

  const checkSavedUser = () => {
    try {
      const savedUser = localStorage.getItem('userInfo')
      if (savedUser) {
        const userInfo = JSON.parse(savedUser)
        setCurrentUser(userInfo)
      }
    } catch (error) {
      console.error('检查已保存用户信息失败:', error)
      localStorage.removeItem('userInfo')
    }
  }

  const loadCategories = async () => {
    try {
      const response = await fetch("/api/public/categories")
      if (response.ok) {
        const data = await response.json()
        setCategories(data.data || data)
      } else {
        // 设置示例分类
        setCategories([
          { id: 1, name: "编程开发", description: "编程相关的教程内容" },
          { id: 2, name: "设计创意", description: "设计和创意相关的教程" },
          { id: 3, name: "商业营销", description: "商业和营销策略教程" },
          { id: 4, name: "生活技能", description: "日常生活技能教程" },
        ])
      }
    } catch (error) {
      console.error("Failed to load categories:", error)
      setCategories([
        { id: 1, name: "编程开发", description: "编程相关的教程内容" },
        { id: 2, name: "设计创意", description: "设计和创意相关的教程" },
        { id: 3, name: "商业营销", description: "商业和营销策略教程" },
        { id: 4, name: "生活技能", description: "日常生活技能教程" },
      ])
    }
  }

  const loadUserUnlocks = async () => {
    try {
      const response = await fetch("/api/user-unlocks")
      if (response.ok) {
        const data = await response.json()
        const unlocks = data.data || data
        setUserUnlocks(unlocks)
        
        // 🎯 关键修复：为每个已解锁教程获取详细进度数据
        const progressMap = new Map()
        const progressPromises = unlocks.map(async (tutorial: any) => {
          try {
            const progressResponse = await fetch(`/api/learning/progress?tutorialId=${tutorial.id}`)
            if (progressResponse.ok) {
              const progressResult = await progressResponse.json()
              if (progressResult.success && progressResult.data.tutorialProgress.length > 0) {
                const latestProgress = progressResult.data.tutorialProgress[0]
                // 🔧 修复：正确获取进度数据，数据存储在learning_data字段中
                const learningData = latestProgress.learning_data || {}
                const progressInfo = {
                  progress: Math.round(learningData.progress_percentage || 0),
                  learning_status: learningData.status || "not_started",
                  last_accessed: learningData.last_updated || tutorial.unlocked_at,
                  reading_time: Math.round((learningData.time_spent || 0) / 60) || 30,
                  difficulty: "intermediate" as const,
                  rating: 4
                }
                progressMap.set(tutorial.id, progressInfo)
                
                console.log(`✅ 主页教程 ${tutorial.id} 进度数据:`, {
                  title: tutorial.title,
                  learningData,
                  finalProgress: progressInfo
                })
                
                return { id: tutorial.id, ...progressInfo }
              }
            }
          } catch (error) {
            console.warn(`Failed to load progress for tutorial ${tutorial.id}:`, error)
          }
          // 默认进度数据
          const defaultProgress = {
            progress: 0,
            learning_status: "not_started" as const,
            last_accessed: tutorial.unlocked_at,
            reading_time: 30,
            difficulty: "intermediate" as const,
            rating: 4
          }
          progressMap.set(tutorial.id, defaultProgress)
          return { id: tutorial.id, ...defaultProgress }
        })
        
        await Promise.all(progressPromises)
        setTutorialProgressData(progressMap)
        
        console.log('✅ 已解锁教程进度数据加载完成:', {
          unlocksCount: unlocks.length,
          progressCount: progressMap.size,
          progressData: Array.from(progressMap.entries())
        })
      }
    } catch (error) {
      console.error("Failed to load user unlocks:", error)
    }
  }

  const verifyKey = async () => {
    if (!keyCode.trim()) {
      toast({ title: "请输入密钥", variant: "destructive" })
      return
    }

    setLoading(true)
    setValidationResult(null)

    try {
      const response = await fetch("/api/verify-key", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ key: keyCode.trim().toUpperCase() }),
      })

      const result = await response.json()

      if (response.ok) {
        // 🎯 第一步：立即更新用户解锁状态，确保UI立即响应
        const latestUnlocks = result.user_unlocks || []
        setUserUnlocks(latestUnlocks)
        setValidationResult({ success: true, tutorial: result.tutorial, user_unlocks: latestUnlocks })
        
        console.log('🎯 密钥验证成功，立即更新状态:', {
          newUnlockId: result.tutorial.id,
          totalUnlocks: latestUnlocks.length,
          unlockedIds: latestUnlocks.map(u => u.id)
        })
        
        // 设置最近解锁的教程ID，用于动画效果
        setRecentlyUnlockedId(result.tutorial.id)
        setTimeout(() => setRecentlyUnlockedId(null), 3000) // 3秒后清除动画状态
        
        // 🎯 第二步：立即强制重新渲染组件，确保教程卡片状态更新
        // 使用函数式更新确保状态正确设置
        setUserUnlocks(prev => {
          const updated = [...latestUnlocks]
          console.log('🔄 强制状态更新:', { prev: prev.length, new: updated.length })
          return updated
        })
        
        // 🎯 第三步：显示成功提示并清空输入
        toast({
          title: "🎉 密钥验证成功！",
          description: `已解锁教程：${result.tutorial.title}，现在可以立即阅读！`,
          duration: 5000,
        })
        setKeyCode("")
        
        // 🎯 第四步：自动滚动到已解锁教程区域（如果有的话）
        setTimeout(() => {
          const unlockedSection = document.querySelector('[data-section="unlocked-tutorials"]')
          if (unlockedSection) {
            unlockedSection.scrollIntoView({ 
              behavior: 'smooth', 
              block: 'start' 
            })
          }
        }, 500) // 延迟滚动，确保状态更新完成
        
        // 🎯 第五步：异步刷新数据确保长期一致性（非阻塞）
        setRefreshing(true)
        Promise.all([
          forceRefresh(), // 刷新教程列表
          loadUserUnlocks() // 再次确认用户解锁状态
        ]).then(() => {
          console.log('✅ 密钥验证后数据刷新完成')
        }).catch(error => {
          console.error('❌ 密钥验证后数据刷新失败:', error)
          // 如果异步刷新失败，确保至少基本状态是正确的
          setUserUnlocks(latestUnlocks)
        }).finally(() => {
          setRefreshing(false)
        })
        
      } else {
        setValidationResult({ success: false, error: result.error })
        toast({
          title: "密钥验证失败",
          description: result.error || "无效的密钥",
          variant: "destructive",
        })
      }
    } catch (error) {
      setValidationResult({ success: false, error: "网络错误，请稍后重试" })
      toast({
        title: "验证错误",
        description: "请稍后重试",
        variant: "destructive",
      })
    } finally {
      setLoading(false)
    }
  }

  const isUnlocked = (tutorialId: number) => {
    return userUnlocks.some((unlock) => unlock.id === tutorialId)
  }
  
  // 获取教程的完整信息（包含进度数据）
  const getTutorialWithProgress = (tutorial: Tutorial): Tutorial => {
    if (isUnlocked(tutorial.id)) {
      const progressInfo = tutorialProgressData.get(tutorial.id)
      if (progressInfo) {
        return {
          ...tutorial,
          is_unlocked: true,
          progress: progressInfo.progress,
          learning_status: progressInfo.learning_status,
          last_accessed: progressInfo.last_accessed,
          reading_time: progressInfo.reading_time,
          difficulty: progressInfo.difficulty,
          rating: progressInfo.rating
        }
      }
    }
    return {
      ...tutorial,
      is_unlocked: isUnlocked(tutorial.id),
      progress: 0,
      learning_status: "not_started",
      reading_time: 30,
      difficulty: "intermediate",
      rating: 4
    }
  }

  // 综合刷新函数
  const refreshAll = async () => {
    setRefreshing(true)
    console.log('🔄 执行全面数据刷新')
    try {
      await Promise.all([
        forceRefresh(), // 刷新教程列表
        loadUserUnlocks(), // 刷新用户解锁状态
        loadCategories() // 刷新分类数据
      ])
      console.log('✅ 全面数据刷新完成')
    } catch (error) {
      console.error('❌ 全面数据刷新失败:', error)
    } finally {
      setRefreshing(false)
    }
  }

  const handleLoginSuccess = (userInfo: any) => {
    setCurrentUser(userInfo)
    // 保存用户信息到localStorage
    try {
      localStorage.setItem('userInfo', JSON.stringify(userInfo))
    } catch (error) {
      console.error('保存用户信息失败:', error)
    }
    // 重新加载用户解锁数据
    loadUserUnlocks()
  }

  const handleLogout = () => {
    setCurrentUser(null)
    setUserUnlocks([])
    // 清除localStorage中的用户信息
    try {
      localStorage.removeItem('userInfo')
    } catch (error) {
      console.error('清除用户信息失败:', error)
    }
    toast({
      title: "已退出登录",
      description: "感谢您的使用",
    })
  }

  // 🎯 修复：使用包含进度数据的教程列表
  const tutorialsWithProgress = tutorials.map(getTutorialWithProgress)
  
  const filteredTutorials = tutorialsWithProgress.filter((tutorial) => {
    const matchesCategory = selectedCategory === "all" || tutorial.category_name === selectedCategory
    const matchesSearch =
      tutorial.title?.toLowerCase().includes(searchQuery.toLowerCase()) ||
      tutorial.description?.toLowerCase().includes(searchQuery.toLowerCase()) ||
      (tutorial.tags && tutorial.tags.some((tag) => tag.toLowerCase().includes(searchQuery.toLowerCase())))
    
    return matchesCategory && matchesSearch
  })

  const unlockedTutorials = filteredTutorials.filter((t) => isUnlocked(t.id))
  const lockedTutorials = filteredTutorials.filter((t) => !isUnlocked(t.id))

  // 对已解锁教程进行排序：按学习进度从低到高，进度满的按最新购买时间排序
  const sortedUnlockedTutorials = unlockedTutorials.sort((a, b) => {
    const progressA = a.progress || 0
    const progressB = b.progress || 0
    
    // 如果两个教程都是100%完成，按最新购买时间排序（最新在前）
    if (progressA === 100 && progressB === 100) {
      const timeA = new Date(a.last_accessed || '').getTime()
      const timeB = new Date(b.last_accessed || '').getTime()
      return timeB - timeA // 时间倒序，最新的在前
    }
    
    // 如果只有一个是100%完成，未完成的优先显示
    if (progressA === 100 && progressB < 100) return 1
    if (progressB === 100 && progressA < 100) return -1
    
    // 都未完成，按进度从低到高排序
    return progressA - progressB
  })

  // 分页逻辑
  const totalPages = Math.ceil(sortedUnlockedTutorials.length / tutorialsPerPage)
  const startIndex = (currentPage - 1) * tutorialsPerPage
  const endIndex = startIndex + tutorialsPerPage
  const currentPageTutorials = sortedUnlockedTutorials.slice(startIndex, endIndex)

  // 当页数变化时重置到第一页
  useEffect(() => {
    if (currentPage > totalPages && totalPages > 0) {
      setCurrentPage(1)
    }
  }, [totalPages, currentPage])

  // 🔧 添加过滤调试信息
  console.log('🔍 过滤调试:', {
    tutorialsCount: tutorials.length,
    selectedCategory,
    searchQuery,
    filteredCount: filteredTutorials.length,
    firstFiltered: filteredTutorials[0],
    userUnlocksCount: userUnlocks.length,
    userUnlocks: userUnlocks.map(u => u.id),
    unlockedCount: unlockedTutorials.length,
    lockedCount: lockedTutorials.length
  })

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div>
              <a href="/admin" className="block hover:opacity-80 transition-opacity">
                <h1 className="text-2xl font-bold text-gray-900">知识商城</h1>
                <p className="text-gray-600">优质教程内容，密钥解锁即享</p>
              </a>
            </div>
            {/* 🎨 精简的右上角功能栏 */}
            <div className="flex items-center space-x-3">
              {/* 状态指示区域 - 去除外边框 */}
              <div className="hidden md:flex items-center space-x-3">
                {/* 可点击的连接状态 - 集成刷新功能 */}
                <button
                  onClick={refreshAll}
                  disabled={tutorialsLoading || refreshing}
                  className="flex items-center space-x-1 transition-colors hover:bg-blue-100 px-2 py-1 rounded-md group"
                  title={tutorialsError ? "连接失败，点击重试" : "数据已同步，点击刷新"}
                >
                  {tutorialsError ? (
                    <>
                      <WifiOff className="h-4 w-4 text-red-600" />
                      <span className="text-xs font-medium text-red-600">离线</span>
                    </>
                  ) : (
                    <>
                      <Wifi className={`h-4 w-4 text-green-600 ${(tutorialsLoading || refreshing) ? 'animate-pulse' : ''}`} />
                      {(tutorialsLoading || refreshing) ? (
                        <RefreshCw className="h-3 w-3 text-green-600 animate-spin ml-1" />
                      ) : (
                        <span className="text-xs font-medium text-green-600 group-hover:text-green-700">在线</span>
                      )}
                    </>
                  )}
                </button>
                
                <Separator orientation="vertical" className="h-4" />
                
                {/* 解锁统计 */}
                <div className="flex items-center space-x-1">
                  <BookOpen className="h-4 w-4 text-blue-600" />
                  <span className="text-sm font-semibold text-blue-700">
                    {userUnlocks.length}
                  </span>
                  <span className="text-xs text-gray-600">已解锁</span>
                </div>
              </div>

              {/* 功能按钮组 - 精简版 */}
              <div className="flex items-center space-x-2">
                {/* 移动端的在线状态按钮 */}
                <button
                  onClick={refreshAll}
                  disabled={tutorialsLoading || refreshing}
                  className="md:hidden flex items-center space-x-1 px-2 py-1 rounded-lg border border-blue-200 bg-blue-50 hover:bg-blue-100 transition-colors"
                  title={tutorialsError ? "连接失败，点击重试" : "点击刷新数据"}
                >
                  {tutorialsError ? (
                    <WifiOff className="h-4 w-4 text-red-600" />
                  ) : (
                    <>
                      <Wifi className={`h-4 w-4 text-green-600 ${(tutorialsLoading || refreshing) ? 'animate-pulse' : ''}`} />
                      {(tutorialsLoading || refreshing) && (
                        <RefreshCw className="h-3 w-3 text-green-600 animate-spin" />
                      )}
                    </>
                  )}
                </button>

                {/* 公告铃铛 */}
                <AnnouncementBell />

                {/* 登录/用户信息按钮 */}
                {currentUser ? (
                  <Button 
                    variant="ghost" 
                    size="sm"
                    onClick={handleLogout}
                    className="flex items-center space-x-1 px-3 py-2 rounded-lg border border-green-200 bg-green-50 text-green-700 hover:bg-green-100 transition-colors"
                    title="点击退出登录"
                  >
                    <MessageCircle className="h-4 w-4" />
                    <span className="hidden sm:inline font-medium">
                      {currentUser.nickname || currentUser.username || '用户'}
                    </span>
                  </Button>
                ) : (
                  <Button 
                    variant="ghost" 
                    size="sm"
                    onClick={() => setShowEmailLogin(true)}
                    className="flex items-center space-x-1 px-3 py-2 rounded-lg border border-blue-200 bg-blue-50 text-blue-700 hover:bg-blue-100 transition-colors"
                  >
                    <MessageCircle className="h-4 w-4" />
                    <span className="hidden sm:inline">登录</span>
                  </Button>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="container mx-auto px-4 py-8">
        {/* Key Redemption Module - Prominent at top */}
        <Card className="mb-8 border-2 border-blue-200 bg-gradient-to-r from-blue-50 to-indigo-50">
          <CardHeader className="text-center">
            <CardTitle className="flex items-center justify-center text-2xl">
              <Key className="h-6 w-6 mr-3 text-blue-600" />
              密钥兑换中心
            </CardTitle>
            <CardDescription className="text-lg">输入您购买的验证密钥，立即解锁对应的教程内容</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="max-w-2xl mx-auto space-y-6">
              <div className="space-y-2">
                <Label htmlFor="key" className="text-base font-medium">
                  验证密钥
                </Label>
                <div className="flex space-x-3">
                  <Input
                    id="key"
                    placeholder="请输入24位密钥（如：ABC123DEF456GHI789JKL012）"
                    value={keyCode}
                    onChange={(e) => setKeyCode(e.target.value.toUpperCase())}
                    maxLength={24}
                    className="font-mono text-lg h-12"
                  />
                  <Button
                    onClick={verifyKey}
                    disabled={loading || keyCode.length !== 24}
                    className="h-12 px-8"
                    size="lg"
                  >
                    {loading ? "验证中..." : "验证密钥"}
                  </Button>
                </div>
              </div>

              {/* Key Validation Status */}
              {validationResult && (
                <div
                  className={`p-4 rounded-lg border ${
                    validationResult.success ? "bg-green-50 border-green-200" : "bg-red-50 border-red-200"
                  }`}
                >
                  <div className="flex items-center">
                    {validationResult.success ? (
                      <CheckCircle className="h-5 w-5 text-green-600 mr-2" />
                    ) : (
                      <AlertCircle className="h-5 w-5 text-red-600 mr-2" />
                    )}
                    <span className={`font-medium ${validationResult.success ? "text-green-800" : "text-red-800"}`}>
                      {validationResult.success ? "密钥验证成功！" : "密钥验证失败"}
                    </span>
                  </div>
                  {validationResult.success && validationResult.tutorial && (
                    <div className="mt-2">
                      <p className="text-green-700">
                        已解锁教程：<strong>{validationResult.tutorial.title}</strong>
                      </p>
                    </div>
                  )}
                  {!validationResult.success && <p className="text-red-700 mt-1">{validationResult.error}</p>}
                </div>
              )}

              <div className="text-center text-sm text-gray-500 space-y-1">
                <p>密钥格式：24位大写字母和数字组合</p>
                <p>每个密钥只能使用一次，请从官方授权渠道购买</p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Search and Filter Section */}
        <div className="mb-6 space-y-4">
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder="搜索教程标题、描述或标签..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <div className="sm:w-48">
              <Select value={selectedCategory} onValueChange={setSelectedCategory}>
                <SelectTrigger>
                  <Filter className="h-4 w-4 mr-2" />
                  <SelectValue placeholder="选择分类" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">全部分类</SelectItem>
                  {categories.map((category) => (
                    <SelectItem key={category.id} value={category.name}>
                      {category.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
        </div>

        {/* My Unlocked Tutorials Section */}
        {sortedUnlockedTutorials.length > 0 && (
          <div className="mb-8" data-section="unlocked-tutorials">
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center">
                <Unlock className="h-5 w-5 mr-2 text-green-600" />
                <h2 className="text-xl font-semibold">我的已解锁教程</h2>
                <Badge className="ml-2 bg-green-100 text-green-800 animate-pulse">
                  {sortedUnlockedTutorials.length}
                </Badge>
                {totalPages > 1 && (
                  <span className="ml-2 text-sm text-gray-500">
                    第 {currentPage} / {totalPages} 页
                  </span>
                )}
              </div>
              <Button variant="outline" onClick={() => router.push('/my-tutorials')}>
                <Library className="h-4 w-4 mr-2" />
                查看全部
              </Button>
            </div>
            
            {/* 教程网格 - 固定一行显示3个 */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
              {currentPageTutorials.map((tutorial) => (
                <TutorialCard 
                  key={tutorial.id} 
                  tutorial={tutorial} 
                  isUnlocked={true}
                  isRecentlyUnlocked={recentlyUnlockedId === tutorial.id}
                  showProgress={true}
                />
              ))}
              
              {/* 如果当前页教程数量不足3个，显示占位卡片保持布局一致 */}
              {currentPageTutorials.length < tutorialsPerPage && (
                <>
                  {Array.from({ length: tutorialsPerPage - currentPageTutorials.length }).map((_, index) => (
                    <div key={`placeholder-${index}`} className="hidden md:block"></div>
                  ))}
                </>
              )}
            </div>

            {/* 分页按钮 */}
            {totalPages > 1 && (
              <div className="flex justify-center items-center space-x-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
                  disabled={currentPage === 1}
                  className="flex items-center"
                >
                  <ChevronLeft className="h-4 w-4 mr-1" />
                  上一页
                </Button>
                
                <div className="flex items-center space-x-1">
                  {Array.from({ length: totalPages }, (_, index) => {
                    const pageNum = index + 1
                    const isCurrentPage = pageNum === currentPage
                    
                    // 只显示当前页附近的页码
                    const shouldShow = (
                      pageNum === 1 || 
                      pageNum === totalPages || 
                      Math.abs(pageNum - currentPage) <= 1
                    )
                    
                    if (!shouldShow) {
                      // 显示省略号
                      if ((pageNum === 2 && currentPage > 4) || (pageNum === totalPages - 1 && currentPage < totalPages - 3)) {
                        return <span key={pageNum} className="px-2 text-gray-400">...</span>
                      }
                      return null
                    }
                    
                    return (
                      <Button
                        key={pageNum}
                        variant={isCurrentPage ? "default" : "outline"}
                        size="sm"
                        onClick={() => setCurrentPage(pageNum)}
                        className={`w-8 h-8 p-0 ${isCurrentPage ? 'bg-blue-600 text-white' : ''}`}
                      >
                        {pageNum}
                      </Button>
                    )
                  })}
                </div>
                
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setCurrentPage(prev => Math.min(totalPages, prev + 1))}
                  disabled={currentPage === totalPages}
                  className="flex items-center"
                >
                  下一页
                  <ChevronRight className="h-4 w-4 ml-1" />
                </Button>
              </div>
            )}
          </div>
        )}

        {/* Available Tutorials Section */}
        <div>
          <div className="flex items-center mb-4">
            <Lock className="h-5 w-5 mr-2 text-gray-600" />
            <h2 className="text-xl font-semibold">可解锁教程</h2>
            <Badge variant="outline" className="ml-2">
              {lockedTutorials.length}
            </Badge>
          </div>

          {tutorialsLoading ? (
            <Card className="text-center py-12">
              <CardContent>
                <CubeSpinner size="md" className="mb-6" />
                <p className="text-gray-500 text-lg">正在加载教程...</p>
                <p className="text-gray-400">请稍候</p>
              </CardContent>
            </Card>
          ) : tutorialsError ? (
            <Card className="text-center py-12">
              <CardContent>
                <AlertCircle className="h-16 w-16 mx-auto text-red-500 mb-4" />
                <p className="text-gray-500 text-lg">加载失败</p>
                <p className="text-gray-400">{tutorialsError.message}</p>
                <Button 
                  onClick={forceRefresh} 
                  variant="outline" 
                  className="mt-4"
                >
                  重新加载
                </Button>
              </CardContent>
            </Card>
          ) : lockedTutorials.length === 0 ? (
            <Card className="text-center py-12">
              <CardContent>
                <BookOpen className="h-16 w-16 mx-auto text-gray-400 mb-4" />
                <p className="text-gray-500 text-lg">没有找到匹配的教程</p>
                <p className="text-gray-400">尝试调整搜索条件或分类筛选</p>
                <div className="mt-4 space-y-2 text-sm text-gray-600">
                  <p>调试信息：</p>
                  <p>原始教程数量: {tutorials.length}</p>
                  <p>过滤后数量: {filteredTutorials.length}</p>
                  <p>已解锁数量: {unlockedTutorials.length}</p>
                  <p>未解锁数量: {lockedTutorials.length}</p>
                </div>
              </CardContent>
            </Card>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {lockedTutorials.map((tutorial) => (
                <TutorialCard 
                  key={tutorial.id} 
                  tutorial={tutorial} 
                  isUnlocked={false}
                  isRecentlyUnlocked={false}
                  showProgress={false}
                />
              ))}
            </div>
          )}
        </div>

        {/* Usage Instructions */}
        <Card className="mt-12 bg-gray-50">
          <CardHeader>
            <CardTitle className="flex items-center">
              <AlertCircle className="h-5 w-5 mr-2" />
              使用说明
            </CardTitle>
          </CardHeader>
          <CardContent className="grid md:grid-cols-2 gap-6 text-sm text-gray-600">
            <div>
              <h4 className="font-medium text-gray-900 mb-2">如何获取密钥？</h4>
              <ul className="space-y-1">
                <li>• 通过官方授权渠道购买教程密钥</li>
                <li>• 每个教程对应唯一的验证密钥</li>
                <li>• 密钥格式为24位大写字母和数字</li>
              </ul>
            </div>
            <div>
              <h4 className="font-medium text-gray-900 mb-2">使用注意事项</h4>
              <ul className="space-y-1">
                <li>• 每个密钥只能使用一次</li>
                <li>• 解锁后的教程永久可访问</li>
                <li>• 请妥善保管您的密钥信息</li>
              </ul>
            </div>
          </CardContent>
        </Card>
      </div>
      {/* 邮箱登录对话框 */}
      <CleanEmailLoginDialog open={showEmailLogin} onOpenChange={setShowEmailLogin} onLoginSuccess={handleLoginSuccess} />
    </div>
  )
}

// Tutorial Card Component - 支持进度显示
function TutorialCard({ tutorial, isUnlocked, isRecentlyUnlocked, showProgress = false }: { 
  tutorial: Tutorial; 
  isUnlocked: boolean;
  isRecentlyUnlocked?: boolean;
  showProgress?: boolean;
}) {
  const [justUnlocked, setJustUnlocked] = useState(false)
  const router = useRouter()
  
  // 智能导航处理：支持Ctrl+点击新窗口，普通点击当前页面跳转
  const handleViewTutorial = (e?: React.MouseEvent) => {
    if (!isUnlocked) return
    
    const url = `/tutorial/${tutorial.id}`
    
    // 检测用户意图：Ctrl+点击、Cmd+点击(Mac)、中键点击 = 新窗口
    if (e && (e.ctrlKey || e.metaKey || e.button === 1)) {
      window.open(url, '_blank')
    } else {
      // 普通点击 = 当前页面跳转，更好的用户体验
      router.push(url)
    }
  }

  // 检测新解锁状态，触发动画效果
  useEffect(() => {
    if (isUnlocked && isRecentlyUnlocked) {
      setJustUnlocked(true)
      const timer = setTimeout(() => setJustUnlocked(false), 2000) // 2秒后移除动画
      return () => clearTimeout(timer)
    }
  }, [isUnlocked, isRecentlyUnlocked])

  return (
    <Card
      className={`h-full flex flex-col transition-all duration-300 hover:shadow-lg ${
        isUnlocked 
          ? "border-green-200 bg-green-50/30 cursor-pointer hover:bg-green-50/50" 
          : "border-gray-200 hover:border-gray-300"
      } ${justUnlocked ? "animate-pulse ring-2 ring-green-400 ring-opacity-50" : ""}`}
      onClick={isUnlocked ? (e) => handleViewTutorial(e) : undefined}
      onMouseDown={isUnlocked ? (e) => e.button === 1 && handleViewTutorial(e) : undefined} // 支持中键点击
    >
      <CardHeader className="pb-2 flex-shrink-0">
        <div className="flex items-start justify-between">
          <div className="flex-1">
            <CardTitle className="text-xl font-bold line-clamp-2 mb-1 min-h-[48px] flex items-start">{tutorial.title}</CardTitle>
            <div className="flex items-center space-x-2 mb-1 -ml-3">
              <Badge variant="secondary" className="text-xs">
                {tutorial.category_name}
              </Badge>
              {isUnlocked ? (
                <Badge className={`text-xs bg-green-100 text-green-800 transition-all duration-300 ${
                  justUnlocked ? "animate-bounce bg-green-200" : ""
                }`}>
                  <Unlock className="h-3 w-3 mr-1" />
                  {tutorial.learning_status === "completed" ? "已完成" : 
                   tutorial.learning_status === "in_progress" ? `进行中 ${tutorial.progress || 0}%` : "可阅读"}
                </Badge>
              ) : (
                <Badge variant="outline" className="text-xs">
                  <Lock className="h-3 w-3 mr-1" />
                  需要密钥
                </Badge>
              )}
              {/* 显示难度等级 */}
              {isUnlocked && tutorial.difficulty && (
                <Badge variant="outline" className={`text-xs ${
                  tutorial.difficulty === "beginner" ? "bg-green-50 text-green-600" :
                  tutorial.difficulty === "intermediate" ? "bg-yellow-50 text-yellow-600" :
                  "bg-red-50 text-red-600"
                }`}>
                  {tutorial.difficulty === "beginner" ? "初级" :
                   tutorial.difficulty === "intermediate" ? "中级" : "高级"}
                </Badge>
              )}
            </div>
          </div>
          {/* 显示评分 */}
          {isUnlocked && tutorial.rating && (
            <div className="flex items-center ml-2">
              {[1, 2, 3, 4, 5].map((star) => (
                <span key={star} className={`text-xs ${
                  star <= tutorial.rating! ? "text-yellow-400" : "text-gray-300"
                }`}>★</span>
              ))}
            </div>
          )}
        </div>
        <CardDescription className="line-clamp-3 min-h-[54px] ml-0">{tutorial.description}</CardDescription>
      </CardHeader>
      
      <CardContent className="pt-0 flex-1 flex flex-col justify-end">
        <div className="space-y-2">
          {/* 进度条显示 - 仅限已解锁且有进度的教程 */}
          {showProgress && isUnlocked && tutorial.learning_status !== "not_started" && (
            <div className="space-y-1">
              <div className="flex justify-between text-sm">
                <span>学习进度</span>
                <span>{tutorial.progress || 0}%</span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div 
                  className="bg-blue-600 h-2 rounded-full transition-all duration-300" 
                  style={{ width: `${tutorial.progress || 0}%` }}
                ></div>
              </div>
            </div>
          )}
          
          {/* 标签区域 - 固定高度 */}
          <div className="min-h-[20px] flex flex-wrap gap-1 -ml-3">
            {tutorial.tags.slice(0, 3).map((tag, index) => (
              <Badge key={index} variant="outline" className="text-xs">
                {tag}
              </Badge>
            ))}
            {tutorial.tags.length > 3 && (
              <Badge variant="outline" className="text-xs">
                +{tutorial.tags.length - 3}
              </Badge>
            )}
          </div>

          <Separator />

          {/* 底部信息区域 - 固定位置 */}
          <div className="flex items-center justify-between min-h-[20px]">
            <div className="flex items-center text-sm text-gray-600">
              <Clock className="h-4 w-4 mr-1" />
              {isUnlocked && tutorial.reading_time ? 
                `${tutorial.reading_time} 分钟` : 
                new Date(tutorial.created_at).toLocaleDateString()}
            </div>
            <div className="text-lg font-semibold text-blue-600">¥{tutorial.price}</div>
          </div>

          {/* 按钮区域 - 固定位置和高度 */}
          {isUnlocked ? (
            <Button 
              className={`w-full h-9 bg-green-600 hover:bg-green-700 text-white transition-all duration-300 ${
                justUnlocked ? "animate-pulse bg-green-500" : ""
              }`}
              variant="default"
              onClick={(e) => {
                e.stopPropagation()
                handleViewTutorial()
              }}
            >
              <BookOpen className="h-4 w-4 mr-2" />
              {tutorial.learning_status === "not_started" ? "开始学习" : 
               tutorial.learning_status === "completed" ? "重新阅读" : "继续学习"}
            </Button>
          ) : (
            <Button 
              className="w-full h-9" 
              variant="outline"
              disabled
            >
              <Lock className="h-4 w-4 mr-2" />
              需要密钥解锁
            </Button>
          )}
        </div>
      </CardContent>
    </Card>
  )
}
