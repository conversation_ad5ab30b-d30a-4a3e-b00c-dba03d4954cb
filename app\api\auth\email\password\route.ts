import { NextRequest, NextResponse } from "next/server"
import { verifyEmailCode, setUserPassword, resetUserPassword } from "@/lib/enhanced-email-auth"

/**
 * 设置/重置密码 API
 * POST /api/auth/email/password
 */
export async function POST(request: NextRequest) {
  try {
    const { email, code, password, action = 'set' } = await request.json()
    
    // 验证必要参数
    if (!email || !code || !password) {
      return NextResponse.json({
        success: false,
        error: "请填写所有必要信息"
      }, { status: 400 })
    }
    
    // 验证操作类型
    if (!['set', 'reset'].includes(action)) {
      return NextResponse.json({
        success: false,
        error: "无效的操作类型"
      }, { status: 400 })
    }
    
    console.log('🔑 密码操作请求:', { email, action })
    
    // 验证码类型映射
    const codeType = action === 'set' ? 'set_password' : 'reset_password'
    
    // 验证邮箱验证码
    const verifyResult = await verifyEmailCode(email, code, codeType)
    
    if (!verifyResult.valid) {
      return NextResponse.json({
        success: false,
        error: verifyResult.error
      }, { status: 400 })
    }
    
    // 执行密码操作
    let passwordResult
    if (action === 'set') {
      passwordResult = await setUserPassword(email, password)
    } else {
      passwordResult = await resetUserPassword(email, password)
    }
    
    if (!passwordResult.success) {
      return NextResponse.json({
        success: false,
        error: passwordResult.error
      }, { status: 400 })
    }
    
    console.log('✅ 密码操作成功:', { email, action })
    
    const messages = {
      set: '密码设置成功！您现在可以使用密码登录',
      reset: '密码重置成功！请使用新密码登录'
    }
    
    return NextResponse.json({
      success: true,
      message: messages[action as keyof typeof messages],
      action
    })
    
  } catch (error) {
    console.error('❌ 密码操作异常:', error)
    
    return NextResponse.json({
      success: false,
      error: "操作过程出现错误"
    }, { status: 500 })
  }
}