import { NextRequest, NextResponse } from "next/server"
import { supabaseAdmin } from "@/lib/supabase"
import crypto from 'crypto'

/**
 * 会话调试API端点
 * 用于实时监控会话创建和验证过程
 */
export async function GET(request: NextRequest) {
  try {
    console.log('🔍 会话调试API被调用')
    
    // 获取当前时间信息
    const serverTime = new Date()
    
    // 暂时跳过数据库时间查询，避免RPC调用错误
    const dbTime = null
    const timeDiff = null
    
    // 查询当前所有会话
    const { data: sessions, error: sessionsError } = await supabaseAdmin
      .from('user_sessions')
      .select(`
        id, user_id, session_token, expires_at, 
        last_activity, created_at,
        users (email, nickname)
      `)
      .order('created_at', { ascending: false })
    
    // 查询最近的登录日志
    const { data: recentLogs, error: logsError } = await supabaseAdmin
      .from('login_logs')
      .select('*')
      .order('created_at', { ascending: false })
      .limit(5)
    
    // 获取Cookie信息
    const sessionToken = request.cookies.get('session_token')?.value
    
    const debugInfo = {
      timestamp: serverTime.toISOString(),
      timing: {
        serverTime: serverTime.toISOString(),
        databaseTime: dbTime?.toISOString() || 'unknown',
        timeDifference: timeDiff ? Math.floor(timeDiff / 1000) + ' seconds' : 'unknown',
        syncStatus: timeDiff && timeDiff < 60000 ? '✅ synced' : '⚠️ out of sync'
      },
      sessions: {
        total: sessions?.length || 0,
        active: sessions?.filter(s => new Date(s.expires_at) > serverTime).length || 0,
        expired: sessions?.filter(s => new Date(s.expires_at) <= serverTime).length || 0,
        details: sessions?.map(s => ({
          userId: s.user_id,
          userEmail: s.users?.email,
          tokenPrefix: s.session_token.substring(0, 8) + '...',
          expiresAt: s.expires_at,
          isExpired: new Date(s.expires_at) <= serverTime,
          timeUntilExpiry: Math.floor((new Date(s.expires_at).getTime() - serverTime.getTime()) / 1000 / 60) + ' minutes'
        })) || []
      },
      cookies: {
        hasSessionToken: !!sessionToken,
        tokenPrefix: sessionToken ? sessionToken.substring(0, 8) + '...' : 'none'
      },
      recentActivity: recentLogs?.map(log => ({
        email: log.email,
        authType: log.auth_type,
        status: log.status,
        createdAt: log.created_at,
        minutesAgo: Math.floor((serverTime.getTime() - new Date(log.created_at).getTime()) / 1000 / 60)
      })) || []
    }
    
    return NextResponse.json({
      success: true,
      debug: debugInfo
    })
    
  } catch (error) {
    console.error('❌ 会话调试API异常:', error)
    
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : '调试API异常',
      timestamp: new Date().toISOString()
    }, { status: 500 })
  }
}

/**
 * 创建测试会话
 */
export async function POST(request: NextRequest) {
  try {
    const { action, userEmail } = await request.json()
    
    if (action === 'create_test_session') {
      console.log('🧪 创建测试会话:', { userEmail })
      
      // 查找用户
      const { data: user, error: userError } = await supabaseAdmin
        .from('users')
        .select('*')
        .eq('email', userEmail)
        .single()
      
      if (userError || !user) {
        return NextResponse.json({
          success: false,
          error: '用户不存在'
        }, { status: 404 })
      }
      
      // 创建会话
      const sessionToken = crypto.randomBytes(32).toString('hex')
      const serverTime = new Date()
      const expiresAt = new Date(serverTime.getTime() + 24 * 60 * 60 * 1000)
      
      console.log('⏰ 会话时间信息:', {
        serverTime: serverTime.toISOString(),
        expiresAt: expiresAt.toISOString(),
        timeUntilExpiry: '24 hours'
      })
      
      const { data: sessionData, error: sessionError } = await supabaseAdmin
        .from('user_sessions')
        .insert({
          user_id: user.id,
          session_token: sessionToken,
          expires_at: expiresAt.toISOString(),
          ip_address: request.ip || '127.0.0.1',
          user_agent: 'Debug API',
          created_at: serverTime.toISOString()
        })
        .select()
        .single()
      
      if (sessionError) {
        console.error('❌ 会话创建失败:', sessionError)
        return NextResponse.json({
          success: false,
          error: '会话创建失败: ' + sessionError.message
        }, { status: 500 })
      }
      
      console.log('✅ 测试会话创建成功:', {
        sessionId: sessionData.id,
        tokenPrefix: sessionToken.substring(0, 8) + '...'
      })
      
      // 设置Cookie并返回
      const response = NextResponse.json({
        success: true,
        message: '测试会话创建成功',
        session: {
          id: sessionData.id,
          tokenPrefix: sessionToken.substring(0, 8) + '...',
          expiresAt: expiresAt.toISOString(),
          user: {
            email: user.email,
            nickname: user.nickname
          }
        }
      })
      
      // 设置Cookie
      response.cookies.set('session_token', sessionToken, {
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production',
        sameSite: 'lax',
        maxAge: 24 * 60 * 60,
        path: '/'
      })
      
      return response
    }
    
    return NextResponse.json({
      success: false,
      error: '未知操作'
    }, { status: 400 })
    
  } catch (error) {
    console.error('❌ 会话调试POST异常:', error)
    
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'POST异常'
    }, { status: 500 })
  }
}