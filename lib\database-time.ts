/**
 * 数据库时间管理工具
 * 解决服务器时间与数据库时间不同步的问题
 */

import { supabaseAdmin } from './supabase'

/**
 * 获取数据库当前时间
 * 确保所有时间操作都使用数据库时间，避免时区和同步问题
 */
export async function getDatabaseTime(): Promise<Date> {
  try {
    const { data, error } = await supabaseAdmin
      .rpc('current_timestamp_utc')
    
    if (error) {
      console.warn('⚠️ 获取数据库时间失败，使用服务器时间:', error.message)
      return new Date()
    }
    
    return new Date(data)
  } catch (e) {
    console.warn('⚠️ 数据库时间查询异常，使用服务器时间:', e)
    return new Date()
  }
}

/**
 * 创建数据库时间戳
 * @param offsetMinutes 偏移分钟数（正数为未来，负数为过去）
 */
export async function createDatabaseTimestamp(offsetMinutes: number = 0): Promise<string> {
  try {
    const { data, error } = await supabaseAdmin
      .rpc('create_timestamp_with_offset', { 
        offset_minutes: offsetMinutes 
      })
    
    if (error) {
      console.warn('⚠️ 创建数据库时间戳失败，使用服务器时间:', error.message)
      const fallbackTime = new Date()
      fallbackTime.setMinutes(fallbackTime.getMinutes() + offsetMinutes)
      return fallbackTime.toISOString()
    }
    
    return data
  } catch (e) {
    console.warn('⚠️ 数据库时间戳创建异常，使用服务器时间:', e)
    const fallbackTime = new Date()
    fallbackTime.setMinutes(fallbackTime.getMinutes() + offsetMinutes)
    return fallbackTime.toISOString()
  }
}

/**
 * 检查时间是否过期（基于数据库时间）
 * @param expiresAt 过期时间字符串
 */
export async function isExpiredByDatabaseTime(expiresAt: string): Promise<boolean> {
  try {
    const { data, error } = await supabaseAdmin
      .rpc('is_timestamp_expired', { 
        expires_at: expiresAt 
      })
    
    if (error) {
      console.warn('⚠️ 检查过期时间失败，使用服务器时间:', error.message)
      return new Date(expiresAt) < new Date()
    }
    
    return data
  } catch (e) {
    console.warn('⚠️ 过期时间检查异常，使用服务器时间:', e)
    return new Date(expiresAt) < new Date()
  }
}

/**
 * 比较两个时间戳的差异（秒）
 * @param timestamp1 时间戳1
 * @param timestamp2 时间戳2（默认为当前数据库时间）
 */
export async function getTimestampDifference(
  timestamp1: string, 
  timestamp2?: string
): Promise<number> {
  try {
    const { data, error } = await supabaseAdmin
      .rpc('get_timestamp_difference', { 
        timestamp1,
        timestamp2: timestamp2 || null
      })
    
    if (error) {
      console.warn('⚠️ 时间差异计算失败，使用服务器时间:', error.message)
      const t1 = new Date(timestamp1)
      const t2 = timestamp2 ? new Date(timestamp2) : new Date()
      return Math.floor((t2.getTime() - t1.getTime()) / 1000)
    }
    
    return data || 0
  } catch (e) {
    console.warn('⚠️ 时间差异计算异常，使用服务器时间:', e)
    const t1 = new Date(timestamp1)
    const t2 = timestamp2 ? new Date(timestamp2) : new Date()
    return Math.floor((t2.getTime() - t1.getTime()) / 1000)
  }
}

/**
 * 格式化数据库时间为本地显示
 * @param timestamp 时间戳
 * @param locale 本地化设置（默认中文）
 */
export function formatDatabaseTime(
  timestamp: string, 
  locale: string = 'zh-CN'
): string {
  try {
    const date = new Date(timestamp)
    return date.toLocaleString(locale, {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
      timeZoneName: 'short'
    })
  } catch (e) {
    console.warn('⚠️ 时间格式化失败:', e)
    return timestamp
  }
}

/**
 * 获取会话过期时间（24小时后）
 */
export async function getSessionExpiryTime(): Promise<string> {
  return await createDatabaseTimestamp(24 * 60) // 24小时 = 1440分钟
}

/**
 * 获取验证码过期时间（10分钟后）
 */
export async function getCodeExpiryTime(): Promise<string> {
  return await createDatabaseTimestamp(10) // 10分钟
}

/**
 * 验证会话是否有效
 * @param expiresAt 会话过期时间
 * @param gracePeriodMinutes 宽限期（分钟）
 */
export async function isSessionValid(
  expiresAt: string, 
  gracePeriodMinutes: number = 5
): Promise<boolean> {
  try {
    const { data, error } = await supabaseAdmin
      .rpc('is_session_valid', { 
        expires_at: expiresAt,
        grace_period_minutes: gracePeriodMinutes
      })
    
    if (error) {
      console.warn('⚠️ 会话有效性检查失败，使用服务器时间:', error.message)
      const expiryTime = new Date(expiresAt)
      const now = new Date()
      now.setMinutes(now.getMinutes() - gracePeriodMinutes)
      return expiryTime > now
    }
    
    return data
  } catch (e) {
    console.warn('⚠️ 会话有效性检查异常，使用服务器时间:', e)
    const expiryTime = new Date(expiresAt)
    const now = new Date()
    now.setMinutes(now.getMinutes() - gracePeriodMinutes)
    return expiryTime > now
  }
}