// 测试修复后的注册流程
require('dotenv').config({ path: '.env.local' });

async function testRegistrationFlow() {
  console.log('🧪 测试修复后的注册流程...\n');
  
  try {
    const testEmail = '<EMAIL>'; // 已知存在的邮箱
    const newEmail = `test${Date.now()}@example.com`; // 新邮箱
    
    // 测试1: 用已存在的邮箱请求注册验证码
    console.log('📝 测试1: 用已存在邮箱请求注册验证码');
    console.log(`   邮箱: ${testEmail}`);
    
    const existingEmailResponse = await fetch('http://localhost:3000/api/auth/email/send-code', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        email: testEmail,
        type: 'register'
      })
    });
    
    const existingEmailResult = await existingEmailResponse.json();
    
    console.log(`   响应状态: ${existingEmailResponse.status}`);
    console.log(`   响应内容:`, existingEmailResult);
    
    if (!existingEmailResult.success && existingEmailResult.error.includes('已被注册')) {
      console.log('✅ 正确阻止了已存在邮箱的注册验证码请求');
    } else {
      console.log('❌ 未正确阻止已存在邮箱的注册验证码请求');
    }
    
    // 测试2: 用新邮箱请求注册验证码
    console.log(`\n📝 测试2: 用新邮箱请求注册验证码`);
    console.log(`   邮箱: ${newEmail}`);
    
    const newEmailResponse = await fetch('http://localhost:3000/api/auth/email/send-code', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        email: newEmail,
        type: 'register'
      })
    });
    
    const newEmailResult = await newEmailResponse.json();
    
    console.log(`   响应状态: ${newEmailResponse.status}`);
    console.log(`   响应内容:`, newEmailResult);
    
    if (newEmailResult.success) {
      console.log('✅ 成功为新邮箱发送注册验证码');
      
      // 如果在开发环境下返回了验证码，测试注册流程
      if (newEmailResult.code) {
        console.log(`   验证码: ${newEmailResult.code}`);
        
        // 测试3: 完整注册流程
        console.log(`\n📝 测试3: 完整注册流程`);
        
        const registerResponse = await fetch('http://localhost:3000/api/auth/email/register', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            email: newEmail,
            code: newEmailResult.code,
            password: 'test123456'
          })
        });
        
        const registerResult = await registerResponse.json();
        
        console.log(`   注册响应状态: ${registerResponse.status}`);
        console.log(`   注册响应内容:`, registerResult);
        
        if (registerResult.success) {
          console.log('✅ 注册成功!');
          console.log(`   用户ID: ${registerResult.user?.id}`);
          console.log(`   用户昵称: ${registerResult.user?.nickname}`);
        } else {
          console.log('❌ 注册失败:', registerResult.error);
        }
      } else {
        console.log('ℹ️ 非开发环境，无法获取验证码进行完整测试');
      }
    } else {
      console.log('❌ 新邮箱发送验证码失败:', newEmailResult.error);
    }
    
    // 测试4: 再次用已存在邮箱尝试注册（如果步骤3成功）
    if (newEmailResult.success && newEmailResult.code) {
      console.log(`\n📝 测试4: 再次用刚注册的邮箱请求验证码`);
      
      const duplicateResponse = await fetch('http://localhost:3000/api/auth/email/send-code', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          email: newEmail,
          type: 'register'
        })
      });
      
      const duplicateResult = await duplicateResponse.json();
      
      console.log(`   响应状态: ${duplicateResponse.status}`);
      console.log(`   响应内容:`, duplicateResult);
      
      if (!duplicateResult.success && duplicateResult.error.includes('已被注册')) {
        console.log('✅ 正确阻止了重复注册请求');
      } else {
        console.log('❌ 未正确阻止重复注册请求');
      }
    }
    
    console.log('\n🎯 注册流程测试总结:');
    console.log('1. 已存在邮箱阻止: ✅');
    console.log('2. 新邮箱验证码发送:', newEmailResult.success ? '✅' : '❌');
    console.log('3. 完整注册流程:', newEmailResult.code ? '已测试' : '需要手动验证');
    
  } catch (error) {
    console.error('❌ 测试过程异常:', error.message);
  }
}

// 执行测试
testRegistrationFlow();