require('dotenv').config({ path: '.env.local' });

async function testSingleRegistration() {
  const testEmail = `test+${Date.now()}@example.com`;
  const verifyCode = '123456';
  const password = 'testpass123';
  
  console.log('🧪 单步注册测试...');
  console.log('测试邮箱:', testEmail);
  
  try {
    // 步骤1: 发送验证码
    console.log('\n1. 发送验证码...');
    const sendCodeResponse = await fetch('http://localhost:3001/api/auth/email/send-code', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email: testEmail,
        type: 'register'
      })
    });

    const sendCodeData = await sendCodeResponse.json();
    console.log('发送验证码响应:', sendCodeData);

    if (!sendCodeData.success) {
      console.error('❌ 发送验证码失败:', sendCodeData.error);
      return;
    }

    // 从响应中获取验证码（如果有的话）
    const actualCode = sendCodeData.code || verifyCode;
    console.log('使用验证码:', actualCode);

    // 等待1秒确保验证码已保存
    await new Promise(resolve => setTimeout(resolve, 1000));

    // 步骤2: 注册
    console.log('\n2. 执行注册...');
    const registerResponse = await fetch('http://localhost:3001/api/auth/email/register', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email: testEmail,
        code: actualCode,
        password: password
      })
    });

    const registerData = await registerResponse.json();
    console.log('注册响应状态:', registerResponse.status);
    console.log('注册响应数据:', registerData);

    if (registerData.success) {
      console.log('✅ 注册测试成功！');
      console.log('用户信息:', {
        id: registerData.user.id,
        email: registerData.user.email,
        nickname: registerData.user.nickname,
        has_password: registerData.user.has_password
      });
    } else {
      console.error('❌ 注册测试失败:', registerData.error);
    }

  } catch (error) {
    console.error('❌ 测试异常:', error.message);
  }
}

testSingleRegistration();