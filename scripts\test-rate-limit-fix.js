// 综合测试频率限制修复结果
const { createClient } = require('@supabase/supabase-js');
require('dotenv').config({ path: '.env.local' });

const supabaseAdmin = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY,
  {
    auth: {
      autoRefreshToken: false,
      persistSession: false
    }
  }
);

async function testRateLimitFix() {
  console.log('🧪 测试频率限制修复结果');
  console.log('=' .repeat(50));
  
  try {
    const testEmail = '<EMAIL>';
    
    // 1. 清理测试数据
    console.log('🧹 清理旧的测试数据...');
    await supabaseAdmin.from('magic_links').delete().eq('email', testEmail);
    await supabaseAdmin.from('users').delete().eq('email', testEmail);
    console.log('✅ 测试数据清理完成');
    
    // 2. 模拟快速连续请求魔法链接
    console.log('\\n🔄 模拟快速连续请求...');
    
    const results = [];
    for (let i = 1; i <= 3; i++) {
      console.log(`\\n📤 发送第 ${i} 次请求...`);
      
      try {
        // 直接调用generateMagicLink函数而不是HTTP API
        const { generateMagicLink } = require('../lib/email-auth.js');
        const result = await generateMagicLink(testEmail);
        
        console.log(`✅ 第 ${i} 次请求成功`);
        console.log(`   Token: ${result.token.substring(0, 8)}...`);
        console.log(`   过期时间: ${result.expiresAt.toISOString()}`);
        
        results.push({ success: true, attempt: i, result });
        
        // 短暂延迟避免时间戳重复
        await new Promise(resolve => setTimeout(resolve, 100));
        
      } catch (error) {
        console.log(`❌ 第 ${i} 次请求失败:`, error.message);
        results.push({ success: false, attempt: i, error: error.message });
      }
    }
    
    // 3. 检查数据库中的记录
    console.log('\\n📊 检查数据库记录...');
    const { data: magicLinks } = await supabaseAdmin
      .from('magic_links')
      .select('*')
      .eq('email', testEmail)
      .order('created_at', { ascending: false });
    
    console.log(`💾 数据库中共有 ${magicLinks?.length || 0} 条魔法链接记录`);
    
    if (magicLinks && magicLinks.length > 0) {
      magicLinks.forEach((link, index) => {
        const age = Math.round((Date.now() - new Date(link.created_at)) / 1000);
        console.log(`   ${index + 1}. 创建: ${age}秒前, 已用: ${link.used ? '是' : '否'}`);
      });
    }
    
    // 4. 测试频率限制逻辑
    console.log('\\n🚦 测试频率限制逻辑...');
    const oneHourAgo = new Date(Date.now() - 60 * 60 * 1000).toISOString();
    
    const { data: recentLinks } = await supabaseAdmin
      .from('magic_links')
      .select('id')
      .eq('email', testEmail)
      .gte('created_at', oneHourAgo);
    
    const count = recentLinks?.length || 0;
    console.log(`📈 1小时内请求次数: ${count}`);
    console.log(`🚫 频率限制状态: ${count >= 5 ? '已触发 (>=5次)' : '正常 (<5次)'}`);
    
    // 5. 分析结果
    console.log('\\n🎯 测试结果分析:');
    const successCount = results.filter(r => r.success).length;
    const failureCount = results.filter(r => !r.success).length;
    
    console.log(`✅ 成功请求: ${successCount}/3`);
    console.log(`❌ 失败请求: ${failureCount}/3`);
    
    if (successCount === 3) {
      console.log('🎉 频率限制问题已修复！连续请求均成功');
    } else if (successCount > 0) {
      console.log('⚠️ 部分请求成功，可能仍有其他问题');
    } else {
      console.log('❌ 所有请求失败，问题尚未解决');
    }
    
    // 6. 清理测试数据
    console.log('\\n🧹 清理测试数据...');
    await supabaseAdmin.from('magic_links').delete().eq('email', testEmail);
    console.log('✅ 测试完成，数据已清理');
    
    return successCount === 3;
    
  } catch (error) {
    console.error('❌ 测试过程异常:', error);
    return false;
  }
}

testRateLimitFix()
  .then(success => {
    console.log('\\n' + '=' .repeat(50));
    if (success) {
      console.log('🎉 频率限制问题修复验证成功！');
      console.log('💡 用户现在可以正常使用邮箱魔法链接登录');
    } else {
      console.log('⚠️ 修复验证未完全成功，可能需要进一步调试');
    }
    process.exit(success ? 0 : 1);
  })
  .catch(error => {
    console.error('测试异常:', error);
    process.exit(1);
  });