// 修复数据库表结构
require('dotenv').config({ path: '.env.local' });
const { createClient } = require('@supabase/supabase-js');

const supabaseAdmin = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY,
  { auth: { autoRefreshToken: false, persistSession: false } }
);

async function fixDatabaseSchema() {
  console.log('🔧 修复数据库表结构...\n');
  
  try {
    console.log('📋 添加密码相关字段到users表');
    
    // 添加 password_hash 字段
    try {
      const { error: passwordHashError } = await supabaseAdmin.rpc('exec_sql', {
        sql: 'ALTER TABLE users ADD COLUMN IF NOT EXISTS password_hash TEXT;'
      });
      
      if (passwordHashError && !passwordHashError.message.includes('already exists')) {
        console.warn('⚠️ 添加password_hash字段可能失败:', passwordHashError.message);
      } else {
        console.log('✅ password_hash 字段已存在或添加成功');
      }
    } catch (e) {
      console.log('ℹ️ 尝试直接使用SQL查询...');
    }
    
    // 添加 has_password 字段
    try {
      const { error: hasPasswordError } = await supabaseAdmin.rpc('exec_sql', {
        sql: 'ALTER TABLE users ADD COLUMN IF NOT EXISTS has_password BOOLEAN DEFAULT FALSE;'
      });
      
      if (hasPasswordError && !hasPasswordError.message.includes('already exists')) {
        console.warn('⚠️ 添加has_password字段可能失败:', hasPasswordError.message);
      } else {
        console.log('✅ has_password 字段已存在或添加成功');
      }
    } catch (e) {
      console.log('ℹ️ 尝试直接使用SQL查询...');
    }
    
    // 添加 password_updated_at 字段
    try {
      const { error: passwordUpdatedError } = await supabaseAdmin.rpc('exec_sql', {
        sql: 'ALTER TABLE users ADD COLUMN IF NOT EXISTS password_updated_at TIMESTAMP WITH TIME ZONE;'
      });
      
      if (passwordUpdatedError && !passwordUpdatedError.message.includes('already exists')) {
        console.warn('⚠️ 添加password_updated_at字段可能失败:', passwordUpdatedError.message);
      } else {
        console.log('✅ password_updated_at 字段已存在或添加成功');
      }
    } catch (e) {
      console.log('ℹ️ 尝试直接使用SQL查询...');
    }
    
    // 验证表结构
    console.log('\n📊 验证users表结构');
    try {
      const { data: tableInfo, error: infoError } = await supabaseAdmin
        .from('users')
        .select('id, email, password_hash, has_password')
        .limit(1);
      
      if (infoError) {
        console.error('❌ 验证表结构失败:', infoError.message);
      } else {
        console.log('✅ users表结构验证成功');
        console.log('   可查询字段: id, email, password_hash, has_password');
      }
    } catch (e) {
      console.error('❌ 表结构验证异常:', e.message);
    }
    
    // 检查email_codes表
    console.log('\n📊 检查email_codes表');
    try {
      const { data: codesInfo, error: codesError } = await supabaseAdmin
        .from('email_codes')
        .select('id, email, code, type')
        .limit(1);
      
      if (codesError) {
        console.error('❌ email_codes表查询失败:', codesError.message);
      } else {
        console.log('✅ email_codes表正常');
      }
    } catch (e) {
      console.error('❌ email_codes表检查异常:', e.message);
    }
    
    console.log('\n🎯 数据库表结构修复完成');
    
  } catch (error) {
    console.error('❌ 修复过程异常:', error.message);
  }
}

// 执行修复
fixDatabaseSchema();