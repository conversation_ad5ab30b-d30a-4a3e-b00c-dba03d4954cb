import { NextRequest, NextResponse } from "next/server"
import { verifyCaptcha } from "@/lib/captcha"

/**
 * 验证图形验证码 API
 * POST /api/captcha/verify
 */
export async function POST(request: NextRequest) {
  try {
    const { sessionId, code } = await request.json()
    
    if (!sessionId || !code) {
      return NextResponse.json({
        success: false,
        error: "缺少必要参数"
      }, { status: 400 })
    }
    
    console.log('🔍 验证图形验证码:', { sessionId, code })
    
    // 验证验证码
    const result = verifyCaptcha(sessionId, code)
    
    if (result.valid) {
      console.log('✅ 图形验证码验证成功')
      return NextResponse.json({
        success: true,
        message: "验证码验证成功"
      })
    } else {
      console.log('❌ 图形验证码验证失败:', result.error)
      return NextResponse.json({
        success: false,
        error: result.error
      }, { status: 400 })
    }
  } catch (error) {
    console.error('❌ 验证码验证异常:', error)
    return NextResponse.json({
      success: false,
      error: "验证过程出现错误"
    }, { status: 500 })
  }
}