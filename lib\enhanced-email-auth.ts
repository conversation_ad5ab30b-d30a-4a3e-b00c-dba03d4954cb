/**
 * 邮箱验证码认证系统
 * 特点：验证码登录 + 可选密码设置 + 安全可靠
 */

import crypto from 'crypto'
import bcrypt from 'bcryptjs'
import { supabaseAdmin } from './supabase'

/**
 * 生成邮箱验证码
 * @param email 邮箱地址
 * @param type 验证码类型
 * @returns 验证码和过期时间
 */
export async function generateEmailCode(
  email: string, 
  type: 'login' | 'register' | 'reset_password' | 'set_password' = 'login'
): Promise<{
  code: string
  expiresAt: Date
}> {
  try {
    // 生成6位数字验证码
    const code = Math.floor(100000 + Math.random() * 900000).toString()
    const expiresAt = new Date(Date.now() + 10 * 60 * 1000) // 10分钟有效期
    
    console.log('📧 生成邮箱验证码:', { email, code, type })
    
    // 保存到数据库
    await supabaseAdmin
      .from('email_codes')
      .insert({
        email,
        code,
        type,
        expires_at: expiresAt.toISOString(),
        used: false,
        attempts: 0,
        created_at: new Date().toISOString()
      })
    
    console.log('✅ 验证码生成成功:', { email, type })
    
    return {
      code,
      expiresAt
    }
  } catch (error) {
    console.error('❌ 验证码生成失败:', error)
    throw new Error('生成验证码失败')
  }
}

/**
 * 验证邮箱验证码
 * @param email 邮箱地址
 * @param code 验证码
 * @param type 验证码类型
 * @returns 验证结果和用户信息
 */
export async function verifyEmailCode(
  email: string,
  code: string,
  type: 'login' | 'register' | 'reset_password' | 'set_password' = 'login'
): Promise<{
  valid: boolean
  user?: any
  error?: string
  isNewUser?: boolean
}> {
  try {
    console.log('🔍 开始验证邮箱验证码:', { email, code, type })
    
    // 从数据库获取验证码信息
    const { data: codeData, error: fetchError } = await supabaseAdmin
      .from('email_codes')
      .select('*')
      .eq('email', email)
      .eq('code', code)
      .eq('type', type)
      .eq('used', false)
      .order('created_at', { ascending: false })
      .limit(1)
      .single()
    
    if (fetchError || !codeData) {
      console.log('❌ 验证码不存在或已使用')
      
      // 增加尝试次数
      await supabaseAdmin
        .from('email_codes')
        .update({ attempts: (codeData?.attempts || 0) + 1 })
        .eq('email', email)
        .eq('type', type)
        .eq('used', false)
      
      return { valid: false, error: '验证码错误或已过期' }
    }
    
    // 检查是否过期
    const expiresAt = new Date(codeData.expires_at)
    if (expiresAt < new Date()) {
      console.log('❌ 验证码已过期')
      return { valid: false, error: '验证码已过期，请重新获取' }
    }
    
    // 检查尝试次数
    if (codeData.attempts >= 5) {
      console.log('❌ 验证码尝试次数过多')
      return { valid: false, error: '验证码尝试次数过多，请重新获取' }
    }
    
    // 标记验证码为已使用
    await supabaseAdmin
      .from('email_codes')
      .update({ 
        used: true, 
        used_at: new Date().toISOString() 
      })
      .eq('id', codeData.id)
    
    // 根据类型处理用户
    let user
    let isNewUser = false
    
    if (type === 'login') {
      // 登录验证码：验证时自动创建用户（无密码登录）
      const result = await getOrCreateUserByEmail(email)
      user = result.user
      isNewUser = result.isNewUser
    } else if (type === 'register') {
      // 注册验证码：验证时不创建用户，只验证验证码有效性
      // 用户将在注册API中创建
      user = null
      isNewUser = true
    } else {
      // 重置密码或设置密码，只获取现有用户
      user = await getUserByEmail(email)
      if (!user) {
        return { valid: false, error: '用户不存在' }
      }
    }
    
    console.log('✅ 邮箱验证码验证成功:', { email, userId: user.id, isNewUser })
    
    return {
      valid: true,
      user,
      isNewUser
    }
  } catch (error) {
    console.error('❌ 邮箱验证码验证异常:', error)
    return { valid: false, error: '验证过程出现错误' }
  }
}

/**
 * 用户密码登录
 * @param email 邮箱地址
 * @param password 密码
 * @returns 登录结果
 */
export async function loginWithPassword(
  email: string,
  password: string
): Promise<{
  success: boolean
  user?: any
  error?: string
}> {
  try {
    console.log('🔍 密码登录验证:', { email })
    
    // 获取用户信息
    const user = await getUserByEmail(email)
    if (!user) {
      return { success: false, error: '用户不存在或邮箱未注册' }
    }
    
    // 检查用户是否设置了密码
    if (!user.password_hash) {
      return { success: false, error: '该账户尚未设置密码，请使用验证码登录' }
    }
    
    // 验证密码
    const isValidPassword = await bcrypt.compare(password, user.password_hash)
    if (!isValidPassword) {
      console.log('❌ 密码验证失败')
      
      // 记录失败尝试
      await logAuthAttempt(email, 'password_login', false, 'invalid_password')
      
      return { success: false, error: '密码错误' }
    }
    
    // 更新最后登录时间
    await supabaseAdmin
      .from('users')
      .update({ 
        last_login_at: new Date().toISOString(),
        login_count: (user.login_count || 0) + 1
      })
      .eq('id', user.id)
    
    // 记录成功登录
    await logAuthAttempt(email, 'password_login', true)
    
    console.log('✅ 密码登录成功:', { email, userId: user.id })
    
    return {
      success: true,
      user: {
        ...user,
        login_count: (user.login_count || 0) + 1,
        last_login_at: new Date().toISOString()
      }
    }
  } catch (error) {
    console.error('❌ 密码登录异常:', error)
    return { success: false, error: '登录过程出现错误' }
  }
}

/**
 * 设置用户密码
 * @param email 邮箱地址
 * @param password 新密码
 * @returns 设置结果
 */
export async function setUserPassword(
  email: string,
  password: string
): Promise<{
  success: boolean
  error?: string
}> {
  try {
    console.log('🔑 设置用户密码:', { email })
    
    // 验证密码强度
    const passwordValidation = validatePassword(password)
    if (!passwordValidation.valid) {
      return { success: false, error: passwordValidation.error }
    }
    
    // 获取用户
    const user = await getUserByEmail(email)
    if (!user) {
      return { success: false, error: '用户不存在' }
    }
    
    // 生成密码哈希
    const saltRounds = 12
    const passwordHash = await bcrypt.hash(password, saltRounds)
    
    // 更新密码
    const { error } = await supabaseAdmin
      .from('users')
      .update({ 
        password_hash: passwordHash,
        has_password: true,
        updated_at: new Date().toISOString()
      })
      .eq('id', user.id)
    
    if (error) {
      console.error('❌ 密码设置失败:', error)
      return { success: false, error: '密码设置失败' }
    }
    
    // 记录密码设置
    await logAuthAttempt(email, 'set_password', true)
    
    console.log('✅ 密码设置成功:', { email, userId: user.id })
    
    return { success: true }
  } catch (error) {
    console.error('❌ 密码设置异常:', error)
    return { success: false, error: '密码设置过程出现错误' }
  }
}

/**
 * 重置用户密码
 * @param email 邮箱地址
 * @param newPassword 新密码
 * @returns 重置结果
 */
export async function resetUserPassword(
  email: string,
  newPassword: string
): Promise<{
  success: boolean
  error?: string
}> {
  try {
    console.log('🔄 重置用户密码:', { email })
    
    // 验证密码强度
    const passwordValidation = validatePassword(newPassword)
    if (!passwordValidation.valid) {
      return { success: false, error: passwordValidation.error }
    }
    
    // 获取用户
    const user = await getUserByEmail(email)
    if (!user) {
      return { success: false, error: '用户不存在' }
    }
    
    // 生成新密码哈希
    const saltRounds = 12
    const passwordHash = await bcrypt.hash(newPassword, saltRounds)
    
    // 更新密码
    const { error } = await supabaseAdmin
      .from('users')
      .update({ 
        password_hash: passwordHash,
        has_password: true,
        updated_at: new Date().toISOString()
      })
      .eq('id', user.id)
    
    if (error) {
      console.error('❌ 密码重置失败:', error)
      return { success: false, error: '密码重置失败' }
    }
    
    // 记录密码重置
    await logAuthAttempt(email, 'reset_password', true)
    
    console.log('✅ 密码重置成功:', { email, userId: user.id })
    
    return { success: true }
  } catch (error) {
    console.error('❌ 密码重置异常:', error)
    return { success: false, error: '密码重置过程出现错误' }
  }
}

/**
 * 创建用户（使用邮箱和密码）
 * @param email 邮箱地址
 * @param password 密码
 * @returns 创建结果
 */
export async function createUserWithPassword(
  email: string,
  password: string
): Promise<{
  success: boolean
  user?: any
  error?: string
}> {
  try {
    console.log('👤 创建用户:', { email })
    
    // 验证密码强度
    const passwordValidation = validatePassword(password)
    if (!passwordValidation.valid) {
      return { success: false, error: passwordValidation.error }
    }
    
    // 生成密码哈希
    const saltRounds = 12
    const passwordHash = await bcrypt.hash(password, saltRounds)
    
    // 创建新用户
    const userId = crypto.randomUUID()
    const username = email.split('@')[0] // 使用邮箱前缀作为用户名
    
    const newUser = {
      id: userId,
      email,
      username,
      nickname: `用户${Math.floor(Math.random() * 1000).toString().padStart(3, '0')}`,
      auth_type: 'email',
      email_verified: true,
      has_password: true,
      password_hash: passwordHash,
      status: 'active',
      login_count: 1,
      created_at: new Date().toISOString(),
      last_login_at: new Date().toISOString()
    }
    
    const { data: createdUser, error: createError } = await supabaseAdmin
      .from('users')
      .insert(newUser)
      .select()
      .single()
    
    if (createError) {
      console.error('❌ 用户创建失败:', createError)
      return { success: false, error: '用户创建失败' }
    }
    
    // 记录注册
    await logAuthAttempt(email, 'register', true)
    
    console.log('✅ 用户创建成功:', { email, userId })
    
    return {
      success: true,
      user: createdUser
    }
  } catch (error) {
    console.error('❌ 用户创建异常:', error)
    return { success: false, error: '用户创建过程出现错误' }
  }
}

/**
 * 获取或创建用户（通过邮箱）
 * @param email 邮箱地址
 * @returns 用户信息和是否为新用户
 */
async function getOrCreateUserByEmail(email: string): Promise<{
  user: any
  isNewUser: boolean
}> {
  try {
    // 先尝试获取现有用户
    const existingUser = await getUserByEmail(email)
    
    if (existingUser) {
      console.log('📍 找到现有用户:', { email, userId: existingUser.id })
      
      // 更新最后登录时间
      await supabaseAdmin
        .from('users')
        .update({ 
          last_login_at: new Date().toISOString(),
          login_count: (existingUser.login_count || 0) + 1,
          email_verified: true
        })
        .eq('id', existingUser.id)
      
      return {
        user: {
          ...existingUser,
          login_count: (existingUser.login_count || 0) + 1,
          last_login_at: new Date().toISOString()
        },
        isNewUser: false
      }
    }
    
    // 创建新用户
    const userId = crypto.randomUUID()
    const username = email.split('@')[0] // 使用邮箱前缀作为用户名
    
    const newUser = {
      id: userId,
      email,
      username,
      nickname: `用户${Math.floor(Math.random() * 1000).toString().padStart(3, '0')}`,
      auth_type: 'email',
      email_verified: true,
      has_password: false,
      status: 'active',
      login_count: 1,
      created_at: new Date().toISOString(),
      last_login_at: new Date().toISOString()
    }
    
    const { data: createdUser, error: createError } = await supabaseAdmin
      .from('users')
      .insert(newUser)
      .select()
      .single()
    
    if (createError) {
      console.error('❌ 用户创建失败:', createError)
      throw new Error('用户创建失败')
    }
    
    console.log('✅ 新用户创建成功:', { email, userId })
    
    return {
      user: createdUser,
      isNewUser: true
    }
  } catch (error) {
    console.error('❌ 获取或创建用户失败:', error)
    throw error
  }
}

/**
 * 通过邮箱获取用户
 * @param email 邮箱地址
 * @returns 用户信息
 */
async function getUserByEmail(email: string) {
  try {
    const { data: user, error } = await supabaseAdmin
      .from('users')
      .select('*')
      .eq('email', email)
      .single()
    
    if (error && error.code !== 'PGRST116') { // PGRST116 = no rows returned
      console.error('❌ 获取用户失败:', error)
      return null
    }
    
    return user
  } catch (error) {
    console.error('❌ 获取用户异常:', error)
    return null
  }
}

/**
 * 发送邮箱验证码
 * @param email 邮箱地址
 * @param code 验证码
 * @param type 验证码类型
 * @returns 发送结果
 */
export async function sendEmailCode(
  email: string,
  code: string,
  type: 'login' | 'register' | 'reset_password' | 'set_password' = 'login'
): Promise<boolean> {
  try {
    console.log('📧 准备发送邮箱验证码:', { email, code, type })
    
    // 生成邮件内容
    const emailContent = generateEmailTemplate(code, type, email)
    
    // 这里可以集成邮件服务，如：
    // - Nodemailer (SMTP)
    // - SendGrid
    // - 腾讯云邮件
    // - 阿里云邮件
    // - AWS SES
    
    // 演示模式：记录到控制台
    const typeMap = {
      login: '登录验证',
      register: '注册验证', 
      reset_password: '重置密码',
      set_password: '设置密码'
    }
    
    console.log(`
╭─────────────────────────────────────╮
│  📧 知识商城 - 邮箱验证码            │
├─────────────────────────────────────┤
│  收件人: ${email.padEnd(25)}       │
│  验证码: ${code}                      │
│  用途: ${typeMap[type]}                       │
│  有效期: 10分钟                      │
╰─────────────────────────────────────╯
    `)
    
    // 实际实现时，使用真实的邮件发送服务
    // const result = await emailService.send({
    //   to: email,
    //   subject: `知识商城 - ${typeMap[type]}验证码`,
    //   html: emailContent
    // })
    
    // 模拟发送成功
    return true
  } catch (error) {
    console.error('❌ 邮箱验证码发送失败:', error)
    return false
  }
}

/**
 * 生成邮件HTML模板
 * @param code 验证码
 * @param type 验证码类型
 * @param email 邮箱地址
 * @returns HTML邮件内容
 */
function generateEmailTemplate(
  code: string, 
  type: 'login' | 'register' | 'reset_password' | 'set_password', 
  email: string
): string {
  const typeInfo = {
    login: { title: '登录验证', action: '登录您的账户', color: '#2563eb' },
    register: { title: '注册验证', action: '完成账户注册', color: '#059669' },
    reset_password: { title: '重置密码', action: '重置您的密码', color: '#dc2626' },
    set_password: { title: '设置密码', action: '设置账户密码', color: '#7c3aed' }
  }
  
  const info = typeInfo[type]
  
  return `
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>知识商城 - ${info.title}</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f6f9fc;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            background-color: #ffffff;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .header {
            background: linear-gradient(135deg, ${info.color} 0%, ${info.color}dd 100%);
            color: white;
            text-align: center;
            padding: 40px 20px;
            position: relative;
            overflow: hidden;
        }
        .header::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
            animation: ripple 3s ease-in-out infinite;
        }
        @keyframes ripple {
            0%, 100% { transform: scale(1); opacity: 0.3; }
            50% { transform: scale(1.1); opacity: 0.1; }
        }
        .logo {
            font-size: 28px;
            font-weight: bold;
            margin-bottom: 10px;
            position: relative;
            z-index: 1;
        }
        .header-title {
            font-size: 18px;
            opacity: 0.9;
            position: relative;
            z-index: 1;
        }
        .content {
            padding: 40px 30px;
        }
        .greeting {
            font-size: 16px;
            margin-bottom: 20px;
            color: #374151;
        }
        .code-section {
            text-align: center;
            margin: 30px 0;
            padding: 30px;
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            border-radius: 12px;
            border: 2px dashed ${info.color}33;
            position: relative;
        }
        .code-label {
            font-size: 14px;
            color: #64748b;
            margin-bottom: 10px;
            text-transform: uppercase;
            letter-spacing: 1px;
            font-weight: 600;
        }
        .verification-code {
            font-size: 36px;
            font-weight: bold;
            color: ${info.color};
            letter-spacing: 6px;
            margin: 15px 0;
            text-shadow: 0 2px 4px rgba(0,0,0,0.1);
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
        }
        .code-note {
            font-size: 12px;
            color: #64748b;
            margin-top: 10px;
        }
        .action-section {
            background-color: #f1f5f9;
            padding: 25px;
            border-radius: 8px;
            margin: 25px 0;
            border-left: 4px solid ${info.color};
        }
        .action-text {
            font-size: 15px;
            color: #1e293b;
            margin-bottom: 15px;
        }
        .steps {
            font-size: 14px;
            color: #475569;
            margin: 0;
            padding-left: 20px;
        }
        .steps li {
            margin: 8px 0;
        }
        .security-notice {
            background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
            border: 1px solid #f59e0b;
            border-radius: 8px;
            padding: 20px;
            margin: 25px 0;
        }
        .security-title {
            font-weight: 600;
            color: #92400e;
            margin-bottom: 10px;
            display: flex;
            align-items: center;
        }
        .security-icon {
            width: 20px;
            height: 20px;
            margin-right: 8px;
        }
        .security-list {
            font-size: 13px;
            color: #92400e;
            margin: 0;
            padding-left: 20px;
        }
        .security-list li {
            margin: 5px 0;
        }
        .footer {
            background-color: #1f2937;
            color: #d1d5db;
            text-align: center;
            padding: 30px 20px;
        }
        .footer-logo {
            font-size: 20px;
            font-weight: bold;
            margin-bottom: 10px;
            color: white;
        }
        .footer-text {
            font-size: 13px;
            opacity: 0.8;
            margin: 5px 0;
        }
        .footer-link {
            color: ${info.color};
            text-decoration: none;
        }
        .divider {
            height: 1px;
            background: linear-gradient(90deg, transparent 0%, #e2e8f0 50%, transparent 100%);
            margin: 25px 0;
        }
        @media only screen and (max-width: 600px) {
            .container { margin: 0; }
            .content { padding: 30px 20px; }
            .verification-code { font-size: 32px; letter-spacing: 4px; }
            .header { padding: 30px 20px; }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <div class="logo">🎓 知识商城</div>
            <div class="header-title">${info.title}验证码</div>
        </div>
        
        <!-- Content -->
        <div class="content">
            <div class="greeting">
                您好！
            </div>
            
            <p>感谢您使用知识商城。您正在进行<strong>${info.action}</strong>操作，请使用以下验证码完成验证：</p>
            
            <!-- Verification Code Section -->
            <div class="code-section">
                <div class="code-label">${info.title}验证码</div>
                <div class="verification-code">${code}</div>
                <div class="code-note">验证码10分钟内有效，请及时使用</div>
            </div>
            
            <!-- How to Use -->
            <div class="action-section">
                <div class="action-text">📋 使用步骤：</div>
                <ol class="steps">
                    <li>返回知识商城网站或应用</li>
                    <li>在验证码输入框中输入：<code>${code}</code></li>
                    <li>点击"验证"按钮完成操作</li>
                    ${type === 'set_password' || type === 'reset_password' ? '<li>验证成功后设置您的新密码</li>' : ''}
                </ol>
            </div>
            
            <div class="divider"></div>
            
            <!-- Security Notice -->
            <div class="security-notice">
                <div class="security-title">
                    <svg class="security-icon" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clip-rule="evenodd"/>
                    </svg>
                    🔒 安全提醒
                </div>
                <ul class="security-list">
                    <li>验证码仅10分钟内有效，过期后需重新获取</li>
                    <li>请勿将验证码告诉他人，知识商城不会主动索取</li>
                    <li>如非本人操作，请忽略此邮件</li>
                    <li>建议设置账户密码，提升账户安全性</li>
                </ul>
            </div>
            
            <p style="color: #64748b; font-size: 14px; margin-top: 30px;">
                如果您在使用过程中遇到任何问题，请及时联系我们的客服团队。
            </p>
        </div>
        
        <!-- Footer -->
        <div class="footer">
            <div class="footer-logo">知识商城</div>
            <div class="footer-text">让学习更简单，让知识更有价值</div>
            <div class="footer-text">
                发送至：${email} | 
                <a href="#" class="footer-link">客服支持</a> | 
                <a href="#" class="footer-link">使用帮助</a>
            </div>
            <div class="footer-text" style="margin-top: 15px; opacity: 0.6;">
                © 2025 知识商城 版权所有
            </div>
        </div>
    </div>
</body>
</html>
  `
}

/**
 * 验证邮箱格式
 * @param email 邮箱地址
 * @returns 是否有效
 */
export function validateEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  return emailRegex.test(email)
}

/**
 * 验证密码强度
 * @param password 密码
 * @returns 验证结果
 */
export function validatePassword(password: string): {
  valid: boolean
  error?: string
  strength?: 'weak' | 'medium' | 'strong'
} {
  if (!password || password.length < 6) {
    return { valid: false, error: '密码长度至少6位' }
  }
  
  if (password.length > 128) {
    return { valid: false, error: '密码长度不能超过128位' }
  }
  
  // 简单的密码强度检查
  let strength: 'weak' | 'medium' | 'strong' = 'weak'
  let score = 0
  
  if (password.length >= 8) score++
  if (/[a-z]/.test(password)) score++
  if (/[A-Z]/.test(password)) score++
  if (/[0-9]/.test(password)) score++
  if (/[^a-zA-Z0-9]/.test(password)) score++
  
  if (score >= 4) strength = 'strong'
  else if (score >= 2) strength = 'medium'
  
  return { valid: true, strength }
}

/**
 * 记录认证尝试
 * @param email 邮箱地址
 * @param authType 认证类型
 * @param success 是否成功
 * @param errorMessage 错误信息
 */
async function logAuthAttempt(
  email: string,
  authType: string,
  success: boolean,
  errorMessage?: string
) {
  try {
    await supabaseAdmin
      .from('login_logs')
      .insert({
        email,
        auth_type: authType,
        status: success ? 'success' : 'failed',
        error_message: errorMessage,
        created_at: new Date().toISOString()
      })
  } catch (error) {
    console.error('❌ 记录认证尝试失败:', error)
  }
}

/**
 * 清理过期的验证码
 * 建议设置定时任务调用此函数
 */
export async function cleanupExpiredEmailCodes(): Promise<number> {
  try {
    const { data, error } = await supabaseAdmin
      .from('email_codes')
      .delete()
      .lt('expires_at', new Date().toISOString())
    
    if (error) {
      console.error('❌ 清理过期验证码失败:', error)
      return 0
    }
    
    const deletedCount = Array.isArray(data) ? data.length : 0
    console.log(`🧹 清理了 ${deletedCount} 个过期的验证码`)
    
    return deletedCount
  } catch (error) {
    console.error('❌ 清理过期验证码异常:', error)
    return 0
  }
}

/**
 * 检查邮箱发送频率限制
 * @param email 邮箱地址
 * @param timeWindow 时间窗口（毫秒）
 * @param maxAttempts 最大尝试次数
 * @returns 是否允许发送
 */
export async function checkEmailRateLimit(
  email: string,
  timeWindow: number = 3600000, // 1小时
  maxAttempts: number = 10
): Promise<boolean> {
  try {
    const windowStart = new Date(Date.now() - timeWindow).toISOString()
    
    const { data: attempts, error } = await supabaseAdmin
      .from('email_codes')
      .select('id')
      .eq('email', email)
      .gte('created_at', windowStart)
    
    if (error) {
      console.error('❌ 邮箱频率限制检查失败:', error)
      return false
    }
    
    const attemptCount = attempts?.length || 0
    
    if (attemptCount >= maxAttempts) {
      console.log(`⚠️ 邮箱 ${email} 超出频率限制: ${attemptCount}/${maxAttempts}`)
      return false
    }
    
    return true
  } catch (error) {
    console.error('❌ 邮箱频率限制检查异常:', error)
    return false
  }
}

/**
 * 配置常量
 */
export const EMAIL_AUTH_CONFIG = {
  CODE_LENGTH: 6, // 验证码长度
  CODE_EXPIRES_MINUTES: 10, // 验证码有效期(分钟)
  MAX_ATTEMPTS: 5, // 最大尝试次数
  MAX_REQUESTS_PER_HOUR: 10, // 每小时最大请求次数
  CLEANUP_INTERVAL_HOURS: 6, // 清理间隔(小时)
  PASSWORD_MIN_LENGTH: 6, // 密码最小长度
  PASSWORD_MAX_LENGTH: 128 // 密码最大长度
} as const