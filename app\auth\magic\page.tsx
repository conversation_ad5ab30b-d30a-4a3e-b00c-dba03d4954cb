"use client"

import { useEffect, useState } from "react"
import { useRouter, useSearchParams } from "next/navigation"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { CheckCircle, AlertCircle, Mail, ArrowLeft, Loader2 } from "lucide-react"
import { useToast } from "@/hooks/use-toast"

export default function MagicLinkPage() {
  const [status, setStatus] = useState<'loading' | 'success' | 'error'>('loading')
  const [message, setMessage] = useState('')
  const [user, setUser] = useState<any>(null)
  const [countdown, setCountdown] = useState(5)
  const router = useRouter()
  const searchParams = useSearchParams()
  const { toast } = useToast()

  useEffect(() => {
    const token = searchParams.get('token')
    const email = searchParams.get('email')
    const signature = searchParams.get('sig')

    console.log('🔍 魔法链接页面参数:', { token, email, signature })

    if (!token || !email || !signature) {
      setStatus('error')
      setMessage('登录链接参数不完整，请重新获取登录链接')
      return
    }

    // 验证魔法链接
    verifyMagicLink(token, email, signature)
  }, [searchParams])

  const verifyMagicLink = async (token: string, email: string, signature: string) => {
    try {
      const url = `/api/auth/magic?token=${encodeURIComponent(token)}&email=${encodeURIComponent(email)}&sig=${encodeURIComponent(signature)}`
      console.log('🔗 验证魔法链接:', url)
      
      const response = await fetch(url)
      const data = await response.json()

      if (data.success) {
        setStatus('success')
        setMessage('登录成功！正在跳转到首页...')
        setUser(data.user)
        
        // 保存用户信息到localStorage
        try {
          localStorage.setItem('userInfo', JSON.stringify(data.user))
        } catch (error) {
          console.error('保存用户信息失败:', error)
        }

        toast({
          title: "登录成功",
          description: `欢迎 ${data.user.nickname || data.user.username}！`,
        })

        // 启动倒计时跳转
        startCountdown()
      } else {
        setStatus('error')
        setMessage(data.error || '登录验证失败')
        console.error('❌ 魔法链接验证失败:', data.error)
        
        toast({
          title: "登录失败",
          description: data.error || '登录链接无效或已过期',
          variant: "destructive",
        })
      }
    } catch (error) {
      console.error('❌ 魔法链接验证异常:', error)
      setStatus('error')
      setMessage('网络错误，请稍后重试')
      
      toast({
        title: "登录错误",
        description: "网络连接异常，请稍后重试",
        variant: "destructive",
      })
    }
  }

  const startCountdown = () => {
    const timer = setInterval(() => {
      setCountdown(prev => {
        if (prev <= 1) {
          clearInterval(timer)
          router.push('/')
          return 0
        }
        return prev - 1
      })
    }, 1000)
  }

  const handleManualRedirect = () => {
    router.push('/')
  }

  const handleBackToLogin = () => {
    router.push('/')
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50 flex items-center justify-center p-4">
      <Card className="w-full max-w-md">
        <CardHeader className="text-center">
          <div className="mx-auto mb-4">
            {status === 'loading' && (
              <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center">
                <Loader2 className="h-8 w-8 text-blue-600 animate-spin" />
              </div>
            )}
            {status === 'success' && (
              <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center">
                <CheckCircle className="h-8 w-8 text-green-600" />
              </div>
            )}
            {status === 'error' && (
              <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center">
                <AlertCircle className="h-8 w-8 text-red-600" />
              </div>
            )}
          </div>
          
          <CardTitle className="flex items-center justify-center">
            <Mail className="h-5 w-5 mr-2 text-blue-600" />
            {status === 'loading' && '正在验证登录...'}
            {status === 'success' && '登录成功！'}
            {status === 'error' && '登录失败'}
          </CardTitle>
          
          <CardDescription>
            {status === 'loading' && '请稍候，正在处理您的登录请求'}
            {status === 'success' && '欢迎回来！即将跳转到首页'}
            {status === 'error' && '登录过程中出现了问题'}
          </CardDescription>
        </CardHeader>

        <CardContent className="text-center space-y-4">
          <p className={`text-sm ${
            status === 'success' ? 'text-green-700' : 
            status === 'error' ? 'text-red-700' : 
            'text-gray-600'
          }`}>
            {message}
          </p>

          {user && status === 'success' && (
            <div className="bg-green-50 border border-green-200 rounded-lg p-3">
              <p className="text-sm text-green-800">
                <strong>用户信息：</strong>
              </p>
              <p className="text-sm text-green-700">
                邮箱: {user.email}
              </p>
              <p className="text-sm text-green-700">
                昵称: {user.nickname || user.username || '用户'}
              </p>
            </div>
          )}

          {status === 'success' && countdown > 0 && (
            <div className="space-y-3">
              <p className="text-sm text-gray-600">
                {countdown} 秒后自动跳转到首页
              </p>
              <Button 
                onClick={handleManualRedirect}
                className="w-full"
              >
                立即跳转
              </Button>
            </div>
          )}

          {status === 'error' && (
            <div className="space-y-3">
              <div className="bg-red-50 border border-red-200 rounded-lg p-3">
                <p className="text-xs text-red-600">
                  可能的原因：
                </p>
                <ul className="text-xs text-red-600 text-left mt-1 space-y-1">
                  <li>• 登录链接已过期（超过10分钟）</li>
                  <li>• 登录链接已被使用过</li>
                  <li>• 链接参数被篡改</li>
                  <li>• 网络连接问题</li>
                </ul>
              </div>
              
              <Button 
                onClick={handleBackToLogin}
                variant="outline"
                className="w-full"
              >
                <ArrowLeft className="h-4 w-4 mr-2" />
                返回首页重新登录
              </Button>
            </div>
          )}

          {status === 'loading' && (
            <div className="space-y-2">
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div className="bg-blue-600 h-2 rounded-full animate-pulse" style={{ width: '60%' }}></div>
              </div>
              <p className="text-xs text-gray-500">
                正在验证您的身份，请稍候...
              </p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}