/**
 * 检查现有数据库表结构
 */
require('dotenv').config({ path: '.env.local' })
const { createClient } = require('@supabase/supabase-js')

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY

const supabase = createClient(supabaseUrl, supabaseServiceKey)

async function checkTableStructure() {
  try {
    console.log('🔍 检查现有数据库表结构...')
    
    // 检查users表
    console.log('\n📋 检查users表:')
    const { data: users, error: usersError } = await supabase
      .from('users')
      .select('*')
      .limit(1)
    
    if (usersError) {
      console.log('❌ users表不存在:', usersError.message)
    } else {
      console.log('✅ users表存在')
      if (users && users.length > 0) {
        console.log('📝 现有字段:', Object.keys(users[0]).sort())
      } else {
        console.log('📝 表为空')
      }
    }
    
    // 检查email_codes表
    console.log('\n📋 检查email_codes表:')
    const { data: codes, error: codesError } = await supabase
      .from('email_codes')
      .select('*')
      .limit(1)
    
    if (codesError) {
      console.log('❌ email_codes表不存在:', codesError.message)
    } else {
      console.log('✅ email_codes表存在')
      if (codes && codes.length > 0) {
        console.log('📝 现有字段:', Object.keys(codes[0]).sort())
      } else {
        console.log('📝 表为空')
      }
    }
    
    // 检查system_config表
    console.log('\n📋 检查system_config表:')
    const { data: config, error: configError } = await supabase
      .from('system_config')
      .select('*')
      .limit(1)
    
    if (configError) {
      console.log('❌ system_config表不存在:', configError.message)
    } else {
      console.log('✅ system_config表存在')
      if (config && config.length > 0) {
        console.log('📝 现有字段:', Object.keys(config[0]).sort())
      } else {
        console.log('📝 表为空')
      }
    }
    
    // 检查其他相关表
    const tables = ['user_sessions', 'login_logs', 'tutorials', 'categories']
    for (const tableName of tables) {
      console.log(`\n📋 检查${tableName}表:`)
      const { data, error } = await supabase
        .from(tableName)
        .select('*')
        .limit(1)
      
      if (error) {
        console.log(`❌ ${tableName}表不存在:`, error.message)
      } else {
        console.log(`✅ ${tableName}表存在`)
        if (data && data.length > 0) {
          console.log('📝 现有字段:', Object.keys(data[0]).sort())
        } else {
          console.log('📝 表为空')
        }
      }
    }
    
  } catch (error) {
    console.error('❌ 检查过程中出现错误:', error)
  }
}

checkTableStructure()