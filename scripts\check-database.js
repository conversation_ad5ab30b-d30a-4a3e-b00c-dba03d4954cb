// 测试数据库连接并检查现有表结构
const path = require('path');
const { createClient } = require('@supabase/supabase-js');
require('dotenv').config({ path: '.env.local' });

console.log('🔧 环境变量检查:');
console.log('SUPABASE_URL:', process.env.NEXT_PUBLIC_SUPABASE_URL ? '已配置' : '未配置');
console.log('SERVICE_ROLE_KEY:', process.env.SUPABASE_SERVICE_ROLE_KEY ? '已配置' : '未配置');

// 创建管理员客户端
const supabaseAdmin = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY,
  {
    auth: {
      autoRefreshToken: false,
      persistSession: false
    }
  }
);

async function checkDatabaseConnection() {
  try {
    console.log('🔍 检查Supabase数据库连接...');
    
    // 测试基本连接
    const { data, error } = await supabaseAdmin
      .from('tutorials')
      .select('count')
      .limit(1);
    
    if (error) {
      console.error('❌ 数据库连接失败:', error.message);
      return false;
    }
    
    console.log('✅ 数据库连接正常');
    
    // 检查现有表结构
    console.log('\n📋 检查现有表结构...');
    
    const tables = [
      'users',
      'magic_links', 
      'email_codes',
      'user_sessions',
      'login_logs',
      'social_accounts',
      'password_history'
    ];
    
    for (const tableName of tables) {
      try {
        const { data, error } = await supabaseAdmin
          .from(tableName)
          .select('*')
          .limit(1);
        
        if (error) {
          console.log(`❌ 表 ${tableName} 不存在:`, error.message);
        } else {
          console.log(`✅ 表 ${tableName} 存在`);
        }
      } catch (err) {
        console.log(`❌ 表 ${tableName} 检查失败:`, err.message);
      }
    }
    
    return true;
    
  } catch (error) {
    console.error('❌ 数据库检查异常:', error);
    return false;
  }
}

// 运行检查
checkDatabaseConnection()
  .then(success => {
    if (success) {
      console.log('\n🎯 数据库状态检查完成');
      console.log('💡 接下来需要执行SQL脚本创建认证表结构');
    } else {
      console.log('\n⚠️ 请检查数据库连接配置');
    }
    process.exit(0);
  })
  .catch(error => {
    console.error('检查过程出错:', error);
    process.exit(1);
  });