/**
 * 图形验证码生成系统
 * 特点：纯JavaScript生成，无需第三方服务，安全可靠
 */

// 生成随机字符串
function generateRandomString(length: number = 4): string {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789'
  let result = ''
  for (let i = 0; i < length; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length))
  }
  return result
}

// 生成随机颜色
function getRandomColor(): string {
  const colors = [
    '#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FECA57',
    '#FF9FF3', '#54A0FF', '#5F27CD', '#00D2D3', '#FF9F43',
    '#341f97', '#ee5253', '#0abde3', '#10ac84', '#f368e0'
  ]
  return colors[Math.floor(Math.random() * colors.length)]
}

// 生成干扰线
function drawInterferenceLine(ctx: CanvasRenderingContext2D, width: number, height: number) {
  ctx.strokeStyle = getRandomColor()
  ctx.lineWidth = 1
  ctx.beginPath()
  ctx.moveTo(Math.random() * width, Math.random() * height)
  ctx.lineTo(Math.random() * width, Math.random() * height)
  ctx.stroke()
}

// 生成干扰点
function drawInterferencePoints(ctx: CanvasRenderingContext2D, width: number, height: number) {
  ctx.fillStyle = getRandomColor()
  for (let i = 0; i < 50; i++) {
    ctx.beginPath()
    ctx.arc(Math.random() * width, Math.random() * height, 1, 0, 2 * Math.PI)
    ctx.fill()
  }
}

// 生成图形验证码
export function generateCaptcha(canvas: HTMLCanvasElement): string {
  const ctx = canvas.getContext('2d')
  if (!ctx) return ''
  
  const width = canvas.width
  const height = canvas.height
  const code = generateRandomString(4)
  
  // 清空画布
  ctx.clearRect(0, 0, width, height)
  
  // 设置背景
  const gradient = ctx.createLinearGradient(0, 0, width, height)
  gradient.addColorStop(0, '#f8fafc')
  gradient.addColorStop(1, '#e2e8f0')
  ctx.fillStyle = gradient
  ctx.fillRect(0, 0, width, height)
  
  // 绘制干扰线
  for (let i = 0; i < 5; i++) {
    drawInterferenceLine(ctx, width, height)
  }
  
  // 绘制验证码文字
  ctx.font = '28px Arial, sans-serif'
  ctx.textBaseline = 'middle'
  
  const charWidth = width / code.length
  for (let i = 0; i < code.length; i++) {
    const char = code[i]
    const x = charWidth * i + charWidth / 2
    const y = height / 2 + (Math.random() - 0.5) * 10 // 随机垂直偏移
    
    // 随机旋转角度
    const angle = (Math.random() - 0.5) * 0.4
    ctx.save()
    ctx.translate(x, y)
    ctx.rotate(angle)
    
    // 设置文字颜色和样式
    ctx.fillStyle = getRandomColor()
    ctx.shadowColor = 'rgba(0,0,0,0.3)'
    ctx.shadowBlur = 2
    ctx.shadowOffsetX = 1
    ctx.shadowOffsetY = 1
    
    ctx.fillText(char, -8, 0)
    ctx.restore()
  }
  
  // 绘制干扰点
  drawInterferencePoints(ctx, width, height)
  
  return code
}

// 验证码会话管理
interface CaptchaSession {
  code: string
  timestamp: number
  attempts: number
}

const captchaSessions = new Map<string, CaptchaSession>()

// 生成会话ID
export function generateSessionId(): string {
  return Math.random().toString(36).substring(2) + Date.now().toString(36)
}

// 创建验证码会话
export function createCaptchaSession(sessionId: string, code: string) {
  captchaSessions.set(sessionId, {
    code: code.toUpperCase(),
    timestamp: Date.now(),
    attempts: 0
  })
  
  // 5分钟后自动清理
  setTimeout(() => {
    captchaSessions.delete(sessionId)
  }, 5 * 60 * 1000)
}

// 验证图形验证码
export function verifyCaptcha(sessionId: string, userInput: string): {
  valid: boolean
  error?: string
} {
  const session = captchaSessions.get(sessionId)
  
  if (!session) {
    return { valid: false, error: '验证码已过期，请刷新后重试' }
  }
  
  // 检查是否过期（5分钟）
  if (Date.now() - session.timestamp > 5 * 60 * 1000) {
    captchaSessions.delete(sessionId)
    return { valid: false, error: '验证码已过期，请刷新后重试' }
  }
  
  // 检查尝试次数
  if (session.attempts >= 3) {
    captchaSessions.delete(sessionId)
    return { valid: false, error: '尝试次数过多，请刷新后重试' }
  }
  
  // 验证码对比
  session.attempts++
  
  if (userInput.toUpperCase() === session.code) {
    captchaSessions.delete(sessionId) // 验证成功后删除会话
    return { valid: true }
  }
  
  return { valid: false, error: '验证码错误，请重新输入' }
}

// 清理过期会话
export function cleanupExpiredSessions() {
  const now = Date.now()
  for (const [sessionId, session] of captchaSessions.entries()) {
    if (now - session.timestamp > 5 * 60 * 1000) {
      captchaSessions.delete(sessionId)
    }
  }
}

// 定期清理过期会话
setInterval(cleanupExpiredSessions, 60 * 1000) // 每分钟清理一次