@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 221.2 83.2% 53.3%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96%;
    --secondary-foreground: 222.2 84% 4.9%;
    --muted: 210 40% 96%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 96%;
    --accent-foreground: 222.2 84% 4.9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 221.2 83.2% 53.3%;
    --radius: 0.5rem;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 217.2 91.2% 59.8%;
    --primary-foreground: 222.2 84% 4.9%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 224.3 76.3% 94.1%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

/* Custom utility classes */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* Prose styles for tutorial content */
.prose {
  color: #374151;
  max-width: none;
}

.prose h1 {
  color: #111827;
  font-weight: 800;
  font-size: 2.25rem;
  margin-top: 0;
  margin-bottom: 0.8888889em;
  line-height: 1.1111111;
}

.prose h2 {
  color: #111827;
  font-weight: 700;
  font-size: 1.875rem;
  margin-top: 2em;
  margin-bottom: 1em;
  line-height: 1.3333333;
}

.prose h3 {
  color: #111827;
  font-weight: 600;
  font-size: 1.5rem;
  margin-top: 1.6em;
  margin-bottom: 0.6em;
  line-height: 1.6;
}

.prose p {
  margin-top: 1.25em;
  margin-bottom: 1.25em;
}

.prose a {
  color: #2563eb;
  text-decoration: underline;
  font-weight: 500;
}

.prose strong {
  color: #111827;
  font-weight: 600;
}

.prose ul {
  margin-top: 1.25em;
  margin-bottom: 1.25em;
  padding-left: 1.625em;
}

.prose li {
  margin-top: 0.5em;
  margin-bottom: 0.5em;
}

.prose blockquote {
  font-weight: 500;
  font-style: italic;
  color: #111827;
  border-left-width: 0.25rem;
  border-left-color: #e5e7eb;
  quotes: "\201C" "\201D" "\2018" "\2019";
  margin-top: 1.6em;
  margin-bottom: 1.6em;
  padding-left: 1em;
}

.prose code {
  color: #111827;
  font-weight: 600;
  font-size: 0.875em;
  background-color: #f3f4f6;
  padding: 0.25em 0.375em;
  border-radius: 0.25rem;
}

.prose pre {
  color: #e5e7eb;
  background-color: #1f2937;
  overflow-x: auto;
  font-size: 0.875em;
  line-height: 1.7142857;
  margin-top: 1.7142857em;
  margin-bottom: 1.7142857em;
  border-radius: 0.375rem;
  padding: 0.8571429em 1.1428571em;
}

/* 学习进度和目录相关样式 */
@layer utilities {
  /* Webkit 滚动条样式 (Chrome, Safari, Edge) */
  .scrollbar-thin {
    scrollbar-width: thin;
    scrollbar-color: #cbd5e1 #f1f5f9;
  }

  .scrollbar-thin::-webkit-scrollbar {
    width: 6px;
  }

  .scrollbar-thin::-webkit-scrollbar-track {
    background: #f1f5f9;
    border-radius: 3px;
  }

  .scrollbar-thin::-webkit-scrollbar-thumb {
    background: #cbd5e1;
    border-radius: 3px;
    transition: background 0.2s ease;
  }

  .scrollbar-thin::-webkit-scrollbar-thumb:hover {
    background: #94a3b8;
  }

  .scrollbar-thumb-gray-300::-webkit-scrollbar-thumb {
    background: #d1d5db;
  }

  .scrollbar-track-gray-100::-webkit-scrollbar-track {
    background: #f3f4f6;
  }

  /* 学习进度相关样式 */
  .progress-indicator {
    position: relative;
  }

  .progress-indicator::before {
    content: '';
    position: absolute;
    left: -2px;
    top: 0;
    width: 2px;
    height: 100%;
    background: linear-gradient(to bottom, #3b82f6, #1d4ed8);
    border-radius: 1px;
    opacity: 0;
    transition: opacity 0.3s ease;
  }

  .progress-indicator.active::before {
    opacity: 1;
  }

  /* 目录项悬停效果 */
  .toc-item {
    transition: all 0.2s ease;
  }

  .toc-item:hover {
    transform: translateX(2px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }

  /* 当前章节高亮 */
  .current-section {
    background: linear-gradient(to right, #eff6ff, #dbeafe);
    border-right: 3px solid #3b82f6;
  }

  /* 完成状态动画 */
  .completion-animation {
    animation: completion-pulse 0.6s ease-in-out;
  }

  @keyframes completion-pulse {
    0% {
      transform: scale(1);
      background-color: transparent;
    }
    50% {
      transform: scale(1.02);
      background-color: #dcfce7;
    }
    100% {
      transform: scale(1);
      background-color: transparent;
    }
  }

  /* 进度条平滑动画 */
  .progress-smooth {
    transition: width 0.5s cubic-bezier(0.4, 0, 0.2, 1);
  }

  /* 固定目录容器 */
  .sticky-toc {
    position: sticky;
    top: 1rem;
    max-height: calc(100vh - 2rem);
    overflow: hidden;
  }

  /* 响应式目录 */
  @media (max-width: 1024px) {
    .sticky-toc {
      position: relative;
      top: 0;
      max-height: none;
      margin-bottom: 1rem;
    }
  }
  
  /* 目录模块专用样式 */
  .toc-container {
    width: 100%;
    max-width: 20rem; /* 320px */
    min-width: 16rem; /* 256px */
  }
  
  /* 固定定位目录样式 */
  .sticky-toc-fixed {
    position: static !important;
    top: auto !important;
    max-height: calc(100vh - 8rem) !important;
    width: 100% !important;
    max-width: 100% !important;
  }
  
  /* 精确对齐系统 - 对齐到返回箭头的最左边 */
  .toc-alignment-container {
    padding-left: calc((100vw - min(100%, 1280px)) / 2 + 1rem);
  }
  
  @media (max-width: 1280px) {
    .toc-alignment-container {
      padding-left: calc((100vw - min(100%, 1024px)) / 2 + 1rem);
    }
    
    .toc-container {
      max-width: 18rem; /* 288px */
      min-width: 14rem; /* 224px */
    }
  }
  
  @media (max-width: 1024px) {
    .toc-alignment-container {
      padding-left: calc((100vw - min(100%, 768px)) / 2 + 1rem);
    }
    
    .toc-container {
      max-width: 16rem; /* 256px */
      min-width: 12rem; /* 192px */
    }
  }
  
  @media (max-width: 768px) {
    .toc-alignment-container {
      padding-left: 1rem;
    }
    
    .toc-container {
      position: relative !important;
      top: 0 !important;
      max-height: none !important;
      max-width: 100%;
      min-width: auto;
      margin-bottom: 1rem;
    }
  }
  
  /* 确保文本不会溢出 */
  .toc-text-safe {
    word-break: break-word;
    hyphens: auto;
    overflow-wrap: break-word;
  }
  
  /* Badge样式优化 */
  .toc-badge {
    font-size: 0.625rem;
    padding: 0.125rem 0.25rem;
    line-height: 1;
    white-space: nowrap;
    max-width: 3rem;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  /* 现代化登录对话框动画效果 */
  @keyframes scale-in {
    0% {
      transform: scale(0);
      opacity: 0;
    }
    50% {
      transform: scale(1.1);
      opacity: 0.8;
    }
    100% {
      transform: scale(1);
      opacity: 1;
    }
  }

  @keyframes slide-down {
    0% {
      transform: translateY(-10px);
      opacity: 0;
    }
    100% {
      transform: translateY(0);
      opacity: 1;
    }
  }

  @keyframes fade-in {
    0% {
      opacity: 0;
    }
    100% {
      opacity: 1;
    }
  }

  @keyframes gradient-shift {
    0% {
      background-position: 0% 50%;
    }
    50% {
      background-position: 100% 50%;
    }
    100% {
      background-position: 0% 50%;
    }
  }

  .animate-scale-in {
    animation: scale-in 0.3s ease-out;
  }

  .animate-slide-down {
    animation: slide-down 0.3s ease-out;
  }

  .animate-fade-in {
    animation: fade-in 0.3s ease-out;
  }

  .animate-gradient {
    background-size: 200% 200%;
    animation: gradient-shift 3s ease infinite;
  }

  /* 输入框焦点增强效果 */
  .input-focus-enhanced {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .input-focus-enhanced:focus {
    transform: translateY(-1px);
    box-shadow: 0 10px 25px -5px rgba(59, 130, 246, 0.25), 
                0 10px 10px -5px rgba(59, 130, 246, 0.04);
  }

  /* 按钮悬停增强效果 */
  .button-hover-enhanced {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
  }

  .button-hover-enhanced::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
  }

  .button-hover-enhanced:hover::before {
    left: 100%;
  }

  /* 玻璃形态效果 */
  .glass-morphism {
    backdrop-filter: blur(16px) saturate(180%);
    background-color: rgba(255, 255, 255, 0.75);
    border: 1px solid rgba(209, 213, 219, 0.3);
  }

  .glass-morphism-dark {
    backdrop-filter: blur(16px) saturate(180%);
    background-color: rgba(17, 24, 39, 0.75);
    border: 1px solid rgba(75, 85, 99, 0.3);
  }

  /* 渐变文本效果 */
  .gradient-text {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  /* 悬浮卡片效果 */
  .floating-card {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .floating-card:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  }

  /* 脉冲动画 */
  .pulse-glow {
    animation: pulse-glow 2s infinite;
  }

  @keyframes pulse-glow {
    0% {
      box-shadow: 0 0 0 0 rgba(59, 130, 246, 0.7);
    }
    70% {
      box-shadow: 0 0 0 10px rgba(59, 130, 246, 0);
    }
    100% {
      box-shadow: 0 0 0 0 rgba(59, 130, 246, 0);
    }
  }
}
